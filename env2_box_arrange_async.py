# Async version of env2-box-arrange.py
from LLM_async import *
from prompt_env2 import *
from env2_create import *
from sre_constants import error
import random
import os
import json
import re
import copy
import numpy as np
import shutil
import time
import asyncio
import hashlib

def calculate_state_hash(pg_dict):
    """Calculate a hash of the current state for deadlock detection"""
    # Create a normalized representation of the state
    state_str = json.dumps(pg_dict, sort_keys=True)
    return hashlib.md5(state_str.encode()).hexdigest()

def detect_deadlock(pg_state_list, response_total_list, min_cycle_length=2, max_lookback=10):
    """Enhanced deadlock detection with multiple detection strategies"""
    if len(pg_state_list) < min_cycle_length * 2:
        return False, None

    # Strategy 1: State-action cycle detection (existing)
    lookback = min(max_lookback, len(pg_state_list))
    recent_states = pg_state_list[-lookback:]
    state_hashes = [calculate_state_hash(state) for state in recent_states]

    # Look for cycles of length 2 to lookback//2
    for cycle_len in range(min_cycle_length, min(lookback//2 + 1, 6)):
        if len(state_hashes) >= cycle_len * 2:
            recent_cycle = state_hashes[-cycle_len:]
            previous_cycle = state_hashes[-cycle_len*2:-cycle_len]

            if recent_cycle == previous_cycle:
                if len(response_total_list) >= cycle_len * 2:
                    response_start_idx = len(response_total_list) - cycle_len * 2
                    recent_resp_cycle = response_total_list[response_start_idx + cycle_len:]
                    previous_resp_cycle = response_total_list[response_start_idx:response_start_idx + cycle_len]

                    if responses_similar(recent_resp_cycle, previous_resp_cycle):
                        print(f"Deadlock detected: State-action cycle of length {cycle_len}")
                        return True, cycle_len
                else:
                    print(f"Deadlock detected: State repetition cycle of length {cycle_len}")
                    return True, cycle_len

    # Strategy 2: Progress stagnation detection
    if len(pg_state_list) >= 5:
        # Check if no progress has been made in the last 5 iterations
        recent_progress_scores = []
        for i in range(-5, 0):
            if abs(i) <= len(pg_state_list):
                state = pg_state_list[i]
                # Calculate a simple progress metric (number of items remaining)
                total_items = sum(len(items) for items in state.values())
                recent_progress_scores.append(total_items)

        if len(set(recent_progress_scores)) == 1 and len(recent_progress_scores) >= 3:
            print(f"Deadlock detected: No progress in last {len(recent_progress_scores)} iterations")
            return True, len(recent_progress_scores)

    # Strategy 3: Collision pattern detection
    if len(response_total_list) >= 4:
        collision_count = 0
        for i in range(-4, 0):
            if abs(i) <= len(response_total_list):
                try:
                    response_dict = json.loads(response_total_list[i])
                    # Check if response contains collision-prone patterns
                    destinations = []
                    for agent, action in response_dict.items():
                        match = re.match(r"move\((.*?),\s(.*?)\)", action)
                        if match:
                            _, location = match.groups()
                            if "position" in location:
                                pos_coords = tuple(map(float, re.findall(r"\d+\.?\d*", location)))
                                destinations.append(pos_coords)

                    # Check for duplicate destinations (potential collisions)
                    if len(destinations) != len(set(destinations)):
                        collision_count += 1
                except:
                    continue

        if collision_count >= 3:
            print(f"Deadlock detected: Repeated collision patterns in {collision_count} recent iterations")
            return True, collision_count

    return False, None

def responses_similar(cycle1, cycle2, similarity_threshold=0.8):
    """Check if two response cycles are similar enough to indicate a deadlock"""
    if len(cycle1) != len(cycle2):
        return False

    similar_count = 0
    for r1, r2 in zip(cycle1, cycle2):
        try:
            dict1 = json.loads(r1) if isinstance(r1, str) else r1
            dict2 = json.loads(r2) if isinstance(r2, str) else r2

            # Count matching actions
            if isinstance(dict1, dict) and isinstance(dict2, dict):
                common_keys = set(dict1.keys()) & set(dict2.keys())
                matching_actions = sum(1 for key in common_keys if dict1[key] == dict2[key])
                total_actions = max(len(dict1), len(dict2))

                if total_actions > 0 and matching_actions / total_actions >= similarity_threshold:
                    similar_count += 1
        except:
            # If parsing fails, do string comparison
            if r1 == r2:
                similar_count += 1

    return similar_count / len(cycle1) >= similarity_threshold

def calculate_progress_score(pg_dict, target_positions):
    """Calculate how close workpieces are to their targets"""
    total_distance = 0
    workpiece_count = 0

    for pos_key, items in pg_dict.items():
        if '_' in pos_key:
            try:
                x, y = map(float, pos_key.split('_'))
                for item in items:
                    if item.startswith('workpiece_'):
                        color = item.replace('workpiece_', '')
                        target_name = f'target_{color}'

                        # Find the closest target of this color
                        min_distance = float('inf')
                        for target_pos_key, target_items in target_positions.items():
                            if target_name in target_items:
                                tx, ty = map(float, target_pos_key.split('_'))
                                distance = abs(x - tx) + abs(y - ty)  # Manhattan distance
                                min_distance = min(min_distance, distance)

                        if min_distance != float('inf'):
                            total_distance += min_distance
                            workpiece_count += 1
            except:
                continue

    # Return negative distance (higher score = better progress)
    return -total_distance if workpiece_count > 0 else 0

def generate_strategic_moves(pg_dict, target_positions, previous_failed_moves=None):
    """Generate strategic moves that prioritize getting workpieces closer to targets"""
    if previous_failed_moves is None:
        previous_failed_moves = set()

    strategic_suggestions = []

    # Find all workpieces and their distances to targets
    workpiece_target_distances = []

    for pos_key, items in pg_dict.items():
        if '_' in pos_key:
            try:
                x, y = map(float, pos_key.split('_'))
                for item in items:
                    if item.startswith('workpiece_'):
                        color = item.replace('workpiece_', '')
                        target_name = f'target_{color}'

                        # Find target positions for this color
                        for target_pos_key, target_items in target_positions.items():
                            if target_name in target_items:
                                tx, ty = map(float, target_pos_key.split('_'))
                                distance = abs(x - tx) + abs(y - ty)
                                workpiece_target_distances.append({
                                    'workpiece': item,
                                    'current_pos': (x, y),
                                    'target_pos': (tx, ty),
                                    'distance': distance,
                                    'color': color
                                })
            except:
                continue

    # Sort by distance (prioritize closer workpieces)
    workpiece_target_distances.sort(key=lambda x: x['distance'])

    # Generate strategic move suggestions
    for wp_info in workpiece_target_distances[:3]:  # Focus on top 3 priorities
        current_x, current_y = wp_info['current_pos']
        target_x, target_y = wp_info['target_pos']

        # Calculate direction towards target
        dx = target_x - current_x
        dy = target_y - current_y

        # Suggest intermediate positions that move towards target
        intermediate_positions = []

        # Try moving one step closer in x or y direction
        if abs(dx) > 0.1:
            new_x = current_x + (0.5 if dx > 0 else -0.5)
            intermediate_positions.append((new_x, current_y))

        if abs(dy) > 0.1:
            new_y = current_y + (0.5 if dy > 0 else -0.5)
            intermediate_positions.append((current_x, new_y))

        # Add diagonal moves if both x and y need adjustment
        if abs(dx) > 0.1 and abs(dy) > 0.1:
            new_x = current_x + (0.5 if dx > 0 else -0.5)
            new_y = current_y + (0.5 if dy > 0 else -0.5)
            intermediate_positions.append((new_x, new_y))

        for new_pos in intermediate_positions:
            move_key = f"{wp_info['workpiece']}_to_{new_pos[0]}_{new_pos[1]}"
            if move_key not in previous_failed_moves:
                strategic_suggestions.append({
                    'workpiece': wp_info['workpiece'],
                    'from_pos': wp_info['current_pos'],
                    'to_pos': new_pos,
                    'priority': 1.0 / (wp_info['distance'] + 1),
                    'reason': f"Move {wp_info['color']} workpiece closer to target"
                })

    return strategic_suggestions

# cen_decen_framework = 'DMAS', 'HMAS-1', 'CMAS', 'HMAS-2'
# dialogue_history_method = '_w_all_dialogue_history', '_wo_any_dialogue_history', '_w_only_state_action_history'
async def run_exp_async(Saving_path, pg_row_num, pg_column_num, iteration_num, query_time_limit, 
                       dialogue_history_method='_w_all_dialogue_history', cen_decen_framework='CMAS', 
                       local_model_choice='gemma-3-27b'):

    Saving_path_result = Saving_path+f'/env_pg_state_{pg_row_num}_{pg_column_num}/pg_state{iteration_num}/{cen_decen_framework}{dialogue_history_method}'

    # specify the path to your dir for saving the results
    os.makedirs(Saving_path_result, exist_ok=True)
    os.makedirs(Saving_path_result+f'/prompt', exist_ok=True)
    os.makedirs(Saving_path_result+f'/response', exist_ok=True)
    os.makedirs(Saving_path_result+f'/pg_state', exist_ok=True)
    os.makedirs(Saving_path_result + f'/dialogue_history', exist_ok=True)

    with open(Saving_path+f'/env_pg_state_{pg_row_num}_{pg_column_num}/pg_state{iteration_num}/pg_state{iteration_num}.json', 'r') as file:
        pg_dict = json.load(file)

    user_prompt_list = [] # The record list of all the input prompts
    response_total_list = [] # The record list of all the responses
    pg_state_list = [] # The record list of apg states in varied steps
    dialogue_history_list = []
    token_num_count_list = []
    pg_state_list.append(pg_dict)
    with open(Saving_path_result+'/pg_state' + '/pg_state'+str(1)+'.json', 'w') as f:
        json.dump(pg_dict, f)

    ### Start the Game! Query LLM for response
    print(f'query_time_limit: {query_time_limit}')
    for index_query_times in range(query_time_limit): # The upper limit of calling LLMs
        state_update_prompt = state_update_func(pg_row_num, pg_column_num, pg_dict)
        
        if cen_decen_framework in ('DMAS'):
            print('--------DMAS method starts (Async)--------')
            match = None
            count_round = 0
            dialogue_history = ''
            response = '{}'

            # Add timeout mechanism for DMAS coordination
            dmas_start_time = time.time()
            dmas_timeout = 120  # 2 minutes timeout for DMAS coordination

            while not match and count_round <= 3:
                # Check DMAS coordination timeout
                if time.time() - dmas_start_time > dmas_timeout:
                    print(f"DMAS COORDINATION TIMEOUT: Breaking after {dmas_timeout} seconds")
                    print("No consensus reached, using empty response to proceed")
                    response = '{}'
                    break
                count_round += 1
                
                # Prepare all agent prompts for parallel processing
                agent_prompts = []
                for local_agent_row_i in range(pg_row_num):
                    for local_agent_column_j in range(pg_column_num):
                        state_update_prompt_local_agent, state_update_prompt_other_agent = state_update_func_local_agent(
                            pg_row_num, pg_column_num, local_agent_row_i, local_agent_column_j, pg_dict)
                        
                        user_prompt_1 = input_prompt_local_agent_DMAS_dialogue_func(
                            state_update_prompt_local_agent, state_update_prompt_other_agent,
                            dialogue_history, response_total_list, pg_state_list, 
                            dialogue_history_list, dialogue_history_method)
                        
                        user_prompt_list.append(user_prompt_1)
                        messages = message_construct_func([user_prompt_1], [], '_w_all_dialogue_history')
                        
                        agent_id = f'Agent[{local_agent_row_i+0.5}, {local_agent_column_j+0.5}]'
                        agent_prompts.append((agent_id, messages))
                
                # Process all agents in parallel
                model_name = get_model_for_framework(cen_decen_framework, 'local', local_model_choice)
                agent_results = await process_agent_requests(agent_prompts, model_name, timeout=30)
                
                # Enhanced DMAS consensus processing
                execute_proposals = []
                agent_responses = {}

                for agent_id, (initial_response, token_count) in agent_results.items():
                    token_num_count_list.append(token_count)
                    dialogue_history += f'[{agent_id}: {initial_response}]\n\n'
                    agent_responses[agent_id] = initial_response

                    if re.search(r'EXECUTE', initial_response):
                        print(f'{agent_id} proposes EXECUTE!')
                        match = re.search(r'{.*}', initial_response, re.DOTALL)
                        if match:
                            proposed_plan = match.group()
                            execute_proposals.append({
                                'agent': agent_id,
                                'plan': proposed_plan,
                                'response': initial_response
                            })

                # DMAS Consensus Logic: Require multiple agents to agree on execution
                if execute_proposals:
                    print(f"DMAS CONSENSUS: {len(execute_proposals)} agents propose execution out of {len(agent_results)}")

                    if len(execute_proposals) >= max(1, len(agent_results) // 2):  # Majority or at least 1
                        # Use the first valid proposal (could be enhanced with plan comparison)
                        selected_proposal = execute_proposals[0]
                        response = selected_proposal['plan']

                        print(f"DMAS CONSENSUS ACHIEVED: Using plan from {selected_proposal['agent']}")

                        # Use appropriate model for syntactic check in DMAS
                        check_model_name = get_model_for_framework(cen_decen_framework, 'local', local_model_choice)
                        response, token_num_count_list_add = await async_with_action_syntactic_check_func(
                            pg_dict, response, [user_prompt_list[-1]], [],
                            check_model_name, '_w_all_dialogue_history')
                        token_num_count_list = token_num_count_list + token_num_count_list_add
                        print(f'DMAS final response: {response}')
                        match = True
                        break
                    else:
                        print(f"DMAS CONSENSUS FAILED: Insufficient execution proposals ({len(execute_proposals)}/{len(agent_results)})")
                        # Continue to next round for more discussion
                else:
                    print("DMAS: No execution proposals in this round, continuing discussion...")

                if match:
                    break
                        
            dialogue_history_list.append(dialogue_history)
            
        elif cen_decen_framework in ('HMAS-2'):
            print('--------HMAS-2 method starts (Async)--------')
            
            # First, get the central planner response
            user_prompt_1 = input_prompt_1_func_total(state_update_prompt, response_total_list,
                                      pg_state_list, dialogue_history_list,
                                      dialogue_history_method, cen_decen_framework)

            # Add strategic intervention if deadlock was detected
            if hasattr(run_exp_async, 'strategic_intervention'):
                user_prompt_1 += run_exp_async.strategic_intervention
                # Clear the intervention after using it
                delattr(run_exp_async, 'strategic_intervention')
                print("Strategic intervention added to central planner prompt")

            user_prompt_list.append(user_prompt_1)
            messages = message_construct_func([user_prompt_1], [], '_w_all_dialogue_history')
            
            with open(Saving_path_result+'/prompt' + '/user_prompt_'+str(index_query_times+1), 'w') as f:
                f.write(user_prompt_list[-1])
                
            # Use appropriate model for the framework (central planner for HMAS-2)
            model_name = get_model_for_framework(cen_decen_framework, 'central', local_model_choice)
            initial_response, token_num_count = await async_GPT_response(messages, model_name)
            print('Initial response: ', initial_response)
            token_num_count_list.append(token_num_count)
            
            response = '{}'  # Initialize response with empty dict
            match = re.search(r'{.*}', initial_response, re.DOTALL)
            if match:
                response = match.group()
                # Use appropriate model for syntactic check
                check_model_name = get_model_for_framework(cen_decen_framework, 'central', local_model_choice)
                response, token_num_count_list_add = await async_with_action_syntactic_check_func(
                    pg_dict, response, [user_prompt_1], [], check_model_name, '_w_all_dialogue_history')
                token_num_count_list = token_num_count_list + token_num_count_list_add
            print(f'response: {response}')

            if response == 'Out of tokens':
                success_failure = 'failure over token length limit'
                return user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result
            elif response == 'Syntactic Error':
                success_failure = 'Syntactic Error'
                return user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result

            # Local agent response for checking the feasibility of actions (HMAS-2 specific)
            break_mark = False
            count_round_HMAS2 = 0

            # Add timeout mechanism for coordination phases
            coordination_start_time = time.time()
            coordination_timeout = 180  # 3 minutes timeout for coordination

            while break_mark == False and count_round_HMAS2 < 3:
                # Check coordination timeout
                if time.time() - coordination_start_time > coordination_timeout:
                    print(f"COORDINATION TIMEOUT: Breaking coordination loop after {coordination_timeout} seconds")
                    print("Proceeding with current plan to avoid infinite coordination loops")
                    break_mark = True
                    break

                count_round_HMAS2 += 1
                dialogue_history = f'Central Planner: {response}\n'

                # Prepare local agent prompts for parallel processing
                agent_prompts = []
                try:
                    agent_dict = json.loads(response)
                except json.JSONDecodeError:
                    print("ERROR: Invalid JSON response from central planner, breaking coordination")
                    break_mark = True
                    break

                for local_agent_row_i in range(pg_row_num):
                    for local_agent_column_j in range(pg_column_num):
                        agent_key = f'Agent[{local_agent_row_i+0.5}, {local_agent_column_j+0.5}]'
                        if agent_key in agent_dict:
                            state_update_prompt_local_agent, state_update_prompt_other_agent = state_update_func_local_agent(
                                pg_row_num, pg_column_num, local_agent_row_i, local_agent_column_j, pg_dict)

                            local_reprompt = input_prompt_local_agent_HMAS2_dialogue_func(
                                state_update_prompt_local_agent, state_update_prompt_other_agent,
                                response, response_total_list, pg_state_list,
                                dialogue_history_list, dialogue_history_method)

                            messages = message_construct_func([local_reprompt], [], '_w_all_dialogue_history')
                            agent_prompts.append((agent_key, messages))

                if not agent_prompts:
                    print("WARNING: No valid agent prompts generated, breaking coordination")
                    break_mark = True
                    break

                # Process all local agents in parallel with timeout
                local_model_name = get_model_for_framework(cen_decen_framework, 'local', local_model_choice)
                try:
                    local_results = await asyncio.wait_for(
                        process_agent_requests(agent_prompts, local_model_name, timeout=45),
                        timeout=60  # Overall timeout for this coordination round
                    )
                except asyncio.TimeoutError:
                    print("TIMEOUT: Local agent coordination took too long, proceeding with current plan")
                    break_mark = True
                    break
                
                # Enhanced consensus mechanism - collect and analyze feedback from local agents
                local_agent_response_list_dir = {'feedback1': ''}
                consensus_votes = {'approve': 0, 'reject': 0, 'modify': 0}
                detailed_feedback = []

                for agent_key, (response_local_agent, token_count) in local_results.items():
                    token_num_count_list.append(token_count)
                    print(f'{agent_key} response: {response_local_agent}')

                    # Enhanced consensus analysis
                    response_lower = response_local_agent.lower()

                    # Check for explicit approval
                    if any(phrase in response_lower for phrase in ['i agree', 'approved', 'looks good', 'plan is acceptable']):
                        consensus_votes['approve'] += 1
                        print(f'{agent_key}: APPROVED')

                    # Check for explicit rejection
                    elif any(phrase in response_lower for phrase in ['i disagree', 'rejected', 'not feasible', 'impossible', 'collision', 'conflict']):
                        consensus_votes['reject'] += 1
                        local_agent_response_list_dir['feedback1'] += f'{agent_key}: {response_local_agent}\n'
                        dialogue_history += f'{agent_key}: {response_local_agent}\n'
                        detailed_feedback.append({'agent': agent_key, 'type': 'rejection', 'reason': response_local_agent})
                        print(f'{agent_key}: REJECTED')

                    # Check for modification requests
                    elif any(phrase in response_lower for phrase in ['suggest', 'modify', 'change', 'alternative', 'instead']):
                        consensus_votes['modify'] += 1
                        local_agent_response_list_dir['feedback1'] += f'{agent_key}: {response_local_agent}\n'
                        dialogue_history += f'{agent_key}: {response_local_agent}\n'
                        detailed_feedback.append({'agent': agent_key, 'type': 'modification', 'reason': response_local_agent})
                        print(f'{agent_key}: MODIFICATION REQUESTED')

                    # Default to rejection if unclear
                    else:
                        consensus_votes['reject'] += 1
                        local_agent_response_list_dir['feedback1'] += f'{agent_key}: {response_local_agent}\n'
                        dialogue_history += f'{agent_key}: {response_local_agent}\n'
                        detailed_feedback.append({'agent': agent_key, 'type': 'unclear', 'reason': response_local_agent})
                        print(f'{agent_key}: UNCLEAR RESPONSE (treated as rejection)')

                # Consensus decision logic
                total_agents = len(local_results)
                approval_rate = consensus_votes['approve'] / total_agents if total_agents > 0 else 0

                print(f"Consensus Analysis: Approve={consensus_votes['approve']}, Reject={consensus_votes['reject']}, Modify={consensus_votes['modify']}")
                print(f"Approval Rate: {approval_rate:.2f} ({consensus_votes['approve']}/{total_agents})")

                # Require majority approval (>50%) for consensus
                if approval_rate > 0.5:
                    print("CONSENSUS ACHIEVED: Majority approval reached")
                    break_mark = True
                elif consensus_votes['approve'] == total_agents:
                    print("UNANIMOUS CONSENSUS: All agents approved")
                    break_mark = True
                else:
                    print(f"CONSENSUS FAILED: Only {approval_rate:.1%} approval rate (need >50%)")

                # Enhanced conflict resolution based on feedback analysis
                if local_agent_response_list_dir['feedback1'] != '':
                    # Analyze feedback patterns for better conflict resolution
                    conflict_summary = ""
                    priority_conflicts = []

                    for feedback in detailed_feedback:
                        if feedback['type'] == 'rejection':
                            if any(keyword in feedback['reason'].lower() for keyword in ['collision', 'conflict', 'occupied']):
                                priority_conflicts.append(f"COLLISION ISSUE from {feedback['agent']}: {feedback['reason']}")
                            elif any(keyword in feedback['reason'].lower() for keyword in ['impossible', 'not reachable', 'cannot']):
                                priority_conflicts.append(f"FEASIBILITY ISSUE from {feedback['agent']}: {feedback['reason']}")

                    if priority_conflicts:
                        conflict_summary = "CRITICAL CONFLICTS DETECTED:\n" + "\n".join(priority_conflicts) + "\n\n"

                    # Enhanced feedback prompt with conflict resolution guidance
                    enhanced_feedback = f"""{conflict_summary}Feedback from local agents:
{local_agent_response_list_dir['feedback1']}

CONFLICT RESOLUTION INSTRUCTIONS:
1. If collision conflicts are reported, ensure no two workpieces target the same position
2. If feasibility issues are reported, check that workpieces exist and targets are reachable
3. Prioritize resolving CRITICAL CONFLICTS first
4. If multiple conflicts exist, address them systematically

The output should have the same json format {{Agent[0.5, 0.5]:move(workpiece_blue, position[0.0, 1.0]), Agent[1.5, 0.5]:move...}}, as above.
Do not explain, just directly output json directory. Your response:"""

                    messages = message_construct_func([user_prompt_list[-1], enhanced_feedback], [response], '_w_all_dialogue_history')

                    # Use central planner model for HMAS-2 re-response
                    central_model_name = get_model_for_framework(cen_decen_framework, 'central', local_model_choice)

                    print(f"CONFLICT RESOLUTION: Sending enhanced feedback to central planner (Round {count_round_HMAS2})")
                    response_central_again, token_num_count = await async_GPT_response(messages, central_model_name)
                    token_num_count_list.append(token_num_count)

                    match = re.search(r'{.*}', response_central_again, re.DOTALL)
                    if match:
                        response = match.group()
                        # Use central planner model for syntactic check in HMAS-2
                        check_model_name = get_model_for_framework(cen_decen_framework, 'central', local_model_choice)
                        response, token_num_count_list_add = await async_with_action_syntactic_check_func(
                            pg_dict, response_central_again, [user_prompt_list[-1], enhanced_feedback],
                            [response], check_model_name, '_w_all_dialogue_history')
                        token_num_count_list = token_num_count_list + token_num_count_list_add
                        print(f'Conflict-resolved plan response: {response}')
                    else:
                        print("WARNING: No valid JSON found in conflict resolution response")
                        break_mark = True
                else:
                    print("CONSENSUS ACHIEVED: No conflicts to resolve")
                    break_mark = True

            dialogue_history_list.append(dialogue_history)
            
        else:
            # Handle CMAS and HMAS-1 frameworks (non-async for now, but can be extended)
            user_prompt_1 = input_prompt_1_func_total(state_update_prompt, response_total_list,
                                      pg_state_list, dialogue_history_list,
                                      dialogue_history_method, cen_decen_framework)

            # Add strategic intervention if deadlock was detected
            if hasattr(run_exp_async, 'strategic_intervention'):
                user_prompt_1 += run_exp_async.strategic_intervention
                # Clear the intervention after using it
                delattr(run_exp_async, 'strategic_intervention')
                print("Strategic intervention added to central planner prompt")

            user_prompt_list.append(user_prompt_1)
            messages = message_construct_func([user_prompt_1], [], '_w_all_dialogue_history')

            with open(Saving_path_result+'/prompt' + '/user_prompt_'+str(index_query_times+1), 'w') as f:
                f.write(user_prompt_list[-1])
                
            # Use appropriate model for the framework
            model_name = get_model_for_framework(cen_decen_framework, 'central', local_model_choice)
            initial_response, token_num_count = await async_GPT_response(messages, model_name)
            print('Initial response: ', initial_response)
            token_num_count_list.append(token_num_count)
            
            response = '{}'  # Initialize response with empty dict
            match = re.search(r'{.*}', initial_response, re.DOTALL)
            if match:
                response = match.group()
                # Use appropriate model for syntactic check
                check_model_name = get_model_for_framework(cen_decen_framework, 'central', local_model_choice)
                response, token_num_count_list_add = await async_with_action_syntactic_check_func(
                    pg_dict, response, [user_prompt_1], [], check_model_name, '_w_all_dialogue_history')
                token_num_count_list = token_num_count_list + token_num_count_list_add
            print(f'response: {response}')

        response_total_list.append(response)
        if response == 'Out of tokens':
            success_failure = 'failure over token length limit'
            return user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result
        elif response == 'Syntactic Error':
            success_failure = 'Syntactic Error'
            return user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result

        data = json.loads(response)
        
        with open(Saving_path_result+'/response' + '/response'+str(index_query_times+1)+'.json', 'w') as f:
            json.dump(data, f)
        original_response_dict = json.loads(response_total_list[index_query_times])
        print(pg_dict)
        
        if cen_decen_framework in ('DMAS', 'HMAS-1', 'HMAS-1-fast'):
            with open(Saving_path_result+'/dialogue_history' + '/dialogue_history'+str(index_query_times)+'.txt', 'w') as f:
                f.write(dialogue_history_list[index_query_times])
                
        try:
            system_error_feedback, pg_dict_returned, collision_check = action_from_response(pg_dict, original_response_dict)
            if system_error_feedback != '':
                print(system_error_feedback)
            if collision_check:
                print('Collision!')
                success_failure = 'Collision'
                return user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result
            pg_dict = pg_dict_returned

        except:
            success_failure = 'Hallucination of wrong plan'
            return user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result
            
        pg_state_list.append(pg_dict)
        with open(Saving_path_result+'/pg_state' + '/pg_state'+str(index_query_times+2)+'.json', 'w') as f:
            json.dump(pg_dict, f)

        # DEADLOCK DETECTION AND STRATEGIC INTERVENTION
        if index_query_times >= 3:  # Start checking after a few iterations
            is_deadlock, cycle_length = detect_deadlock(pg_state_list, response_total_list)

            if is_deadlock:
                print(f'DEADLOCK DETECTED! Cycle length: {cycle_length}')
                print('Implementing strategic intervention...')

                # Find target positions from the initial state
                initial_state = pg_state_list[0]
                target_positions = {}
                for pos_key, items in initial_state.items():
                    targets_in_pos = [item for item in items if item.startswith('target_')]
                    if targets_in_pos:
                        target_positions[pos_key] = targets_in_pos

                # Calculate progress score
                current_progress = calculate_progress_score(pg_dict, target_positions)
                print(f'Current progress score: {current_progress}')

                # Generate strategic moves
                failed_moves = set()
                # Extract failed moves from recent history
                for i in range(max(0, len(response_total_list) - cycle_length * 2), len(response_total_list)):
                    try:
                        resp_dict = json.loads(response_total_list[i])
                        for agent, action in resp_dict.items():
                            # Extract workpiece and target position from action
                            match = re.match(r"move\((.*?),\s(.*?)\)", action)
                            if match:
                                workpiece, location = match.groups()
                                if "position" in location:
                                    pos_coords = tuple(map(float, re.findall(r"\d+\.?\d*", location)))
                                    move_key = f"{workpiece}_to_{pos_coords[0]}_{pos_coords[1]}"
                                    failed_moves.add(move_key)
                    except:
                        continue

                strategic_moves = generate_strategic_moves(pg_dict, target_positions, failed_moves)

                if strategic_moves:
                    print('Strategic moves suggested:')
                    for move in strategic_moves[:3]:  # Show top 3 suggestions
                        print(f"  - {move['reason']}: Move {move['workpiece']} from {move['from_pos']} to {move['to_pos']}")

                    # Create a strategic intervention prompt
                    strategic_prompt_addition = f"""

                    CRITICAL: DEADLOCK DETECTED! The system has been repeating the same {cycle_length}-step cycle.

                    Previous attempts have failed. You must try a DIFFERENT approach:

                    Strategic suggestions based on goal analysis:
                    """

                    for i, move in enumerate(strategic_moves[:3]):
                        strategic_prompt_addition += f"""
                    {i+1}. {move['reason']}: Consider moving {move['workpiece']} towards position {move['to_pos']}
                       (Priority: {move['priority']:.2f}, Current distance from target: {1/move['priority']-1:.1f})"""

                    strategic_prompt_addition += f"""

                    AVOID these recently failed moves: {', '.join(list(failed_moves)[:5])}

                    Focus on making PROGRESS towards the goal rather than repeating previous actions.
                    Choose moves that get workpieces closer to their color-coded targets.
                    """

                    # Store the strategic intervention for the next iteration
                    if not hasattr(run_exp_async, 'strategic_intervention'):
                        run_exp_async.strategic_intervention = strategic_prompt_addition

                    # Log the intervention
                    with open(Saving_path_result+'/deadlock_intervention_'+str(index_query_times+1)+'.txt', 'w') as f:
                        f.write(f"Deadlock detected at iteration {index_query_times+1}\n")
                        f.write(f"Cycle length: {cycle_length}\n")
                        f.write(f"Progress score: {current_progress}\n")
                        f.write(f"Strategic intervention:\n{strategic_prompt_addition}")

        # Check whether the task has been completed
        count = 0
        for key, value in pg_dict.items():
            count += len(value)
        if count == 0:
            break

    if index_query_times < query_time_limit - 1:
        success_failure = 'success'
    else:
        success_failure = 'failure over query time limit'
    return user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result
