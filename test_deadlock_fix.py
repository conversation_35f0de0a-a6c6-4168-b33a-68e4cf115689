#!/usr/bin/env python3
"""
Test script to verify that the deadlock detection and strategic intervention fixes work correctly.
This script specifically tests the problematic 2x2 grid, iteration 3 scenario that was stuck in infinite loop.
"""

import asyncio
import os
import time
import sys
import json

# Add current directory to path to import the async module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the enhanced async function
from env2_box_arrange_async import run_exp_async, detect_deadlock, calculate_progress_score, generate_strategic_moves, calculate_state_hash

Code_dir_path = os.path.dirname(os.path.abspath(__file__)) + '/'
Saving_path = Code_dir_path + 'Env2_WorkpieceNet2'

# Configuration for the problematic scenario
cen_decen_framework = 'HMAS-2'
local_model_choice = 'gemma-3-27b'
pg_row_num, pg_column_num = 2, 2  # The problematic grid size
iteration_num = 3  # The specific iteration that was failing
query_time_limit = 15  # Reduced limit to test deadlock detection faster

async def test_deadlock_detection():
    """Test the deadlock detection functionality with known problematic states"""
    print("=== Testing Deadlock Detection ===")
    
    # Create mock states that represent the infinite loop pattern
    mock_state_1 = {
        "0.5_0.5": ["target_purple"],
        "0.5_1.5": ["target_green"], 
        "1.5_0.5": ["target_blue", "target_red"],
        "1.5_1.5": ["target_orange"],
        "0.0_1.0": ["workpiece_purple"],
        "1.0_2.0": ["workpiece_red"],
        "2.0_0.0": ["workpiece_green"],
        "2.0_1.0": ["workpiece_blue"],
        "2.0_2.0": ["workpiece_orange"]
    }
    
    mock_state_2 = {
        "0.5_0.5": ["target_purple"],
        "0.5_1.5": ["target_green"], 
        "1.5_0.5": ["target_blue", "target_red"],
        "1.5_1.5": ["target_orange"],
        "1.0_0.0": ["workpiece_purple"],  # Different position
        "1.0_2.0": ["workpiece_red"],
        "2.0_0.0": ["workpiece_green"],
        "2.0_1.0": ["workpiece_blue"],
        "2.0_2.0": ["workpiece_orange"]
    }
    
    # Create alternating pattern (state1 -> state2 -> state1 -> state2)
    mock_states = [mock_state_1, mock_state_2, mock_state_1, mock_state_2, mock_state_1, mock_state_2]

    mock_responses = [
        '{"Agent[0.5, 0.5]": "move(workpiece_purple, position(1.0, 0.0))", "Agent[0.5, 1.5]": "move(workpiece_red, position(1.0, 2.0))", "Agent[1.5, 0.5]": "move(workpiece_green, position(2.0, 0.0))", "Agent[1.5, 1.5]": "move(workpiece_orange, position(2.0, 2.0))"}',
        '{"Agent[0.5, 0.5]": "move(workpiece_purple, position(0.0, 1.0))", "Agent[0.5, 1.5]": "move(workpiece_red, position(1.0, 2.0))", "Agent[1.5, 0.5]": "move(workpiece_green, position(2.0, 0.0))", "Agent[1.5, 1.5]": "move(workpiece_orange, position(2.0, 2.0))"}',
        '{"Agent[0.5, 0.5]": "move(workpiece_purple, position(1.0, 0.0))", "Agent[0.5, 1.5]": "move(workpiece_red, position(1.0, 2.0))", "Agent[1.5, 0.5]": "move(workpiece_green, position(2.0, 0.0))", "Agent[1.5, 1.5]": "move(workpiece_orange, position(2.0, 2.0))"}',
        '{"Agent[0.5, 0.5]": "move(workpiece_purple, position(0.0, 1.0))", "Agent[0.5, 1.5]": "move(workpiece_red, position(1.0, 2.0))", "Agent[1.5, 0.5]": "move(workpiece_green, position(2.0, 0.0))", "Agent[1.5, 1.5]": "move(workpiece_orange, position(2.0, 2.0))"}',
        '{"Agent[0.5, 0.5]": "move(workpiece_purple, position(1.0, 0.0))", "Agent[0.5, 1.5]": "move(workpiece_red, position(1.0, 2.0))", "Agent[1.5, 0.5]": "move(workpiece_green, position(2.0, 0.0))", "Agent[1.5, 1.5]": "move(workpiece_orange, position(2.0, 2.0))"}'
    ]

    print(f"Mock states length: {len(mock_states)}")
    print(f"Mock responses length: {len(mock_responses)}")
    print("State hashes:")
    for i, state in enumerate(mock_states):
        hash_val = calculate_state_hash(state)
        print(f"  State {i}: {hash_val[:8]}...")
    print("Response comparison:")
    for i, resp in enumerate(mock_responses):
        print(f"  Response {i}: {resp[:50]}...")
    
    # Test deadlock detection
    is_deadlock, cycle_length = detect_deadlock(mock_states, mock_responses)
    
    print(f"Deadlock detected: {is_deadlock}")
    print(f"Cycle length: {cycle_length}")
    
    if is_deadlock and cycle_length == 2:
        print("✅ Deadlock detection working correctly!")
    else:
        print("❌ Deadlock detection failed!")
        return False
    
    # Test progress calculation
    target_positions = {
        "0.5_0.5": ["target_purple"],
        "0.5_1.5": ["target_green"], 
        "1.5_0.5": ["target_blue", "target_red"],
        "1.5_1.5": ["target_orange"]
    }
    
    progress_score = calculate_progress_score(mock_state_1, target_positions)
    print(f"Progress score: {progress_score}")
    
    # Test strategic move generation
    strategic_moves = generate_strategic_moves(mock_state_1, target_positions)
    print(f"Strategic moves generated: {len(strategic_moves)}")
    
    if strategic_moves:
        print("Top strategic suggestions:")
        for i, move in enumerate(strategic_moves[:3]):
            print(f"  {i+1}. {move['reason']}")
        print("✅ Strategic move generation working!")
    else:
        print("❌ Strategic move generation failed!")
        return False
    
    return True

async def test_enhanced_execution():
    """Test the enhanced execution with deadlock detection on the problematic scenario"""
    print("\n=== Testing Enhanced Execution ===")
    print(f'Framework: {cen_decen_framework}, Local Model: {local_model_choice}')
    print(f'Grid: {pg_row_num}x{pg_column_num}, Iteration: {iteration_num}')
    
    start_time = time.time()
    
    try:
        user_prompt_list, response_total_list, pg_state_list, success_failure, index_query_times, token_num_count_list, Saving_path_result = await run_exp_async(
            Saving_path, pg_row_num, pg_column_num, iteration_num, query_time_limit, 
            dialogue_history_method='_w_only_state_action_history',
            cen_decen_framework=cen_decen_framework, 
            local_model_choice=local_model_choice
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"\n=== Execution Results ===")
        print(f"Success/Failure: {success_failure}")
        print(f"Iterations completed: {index_query_times + 1}")
        print(f"Execution time: {execution_time:.2f} seconds")
        print(f"Total tokens used: {sum(token_num_count_list)}")
        
        # Check if deadlock intervention files were created
        deadlock_files = []
        for i in range(1, index_query_times + 2):
            deadlock_file = f"{Saving_path_result}/deadlock_intervention_{i}.txt"
            if os.path.exists(deadlock_file):
                deadlock_files.append(deadlock_file)
        
        if deadlock_files:
            print(f"\n✅ Deadlock interventions triggered: {len(deadlock_files)} times")
            print("Intervention files created:")
            for file in deadlock_files:
                print(f"  - {file}")
                
            # Show content of the first intervention
            with open(deadlock_files[0], 'r') as f:
                content = f.read()
                print(f"\nFirst intervention content:\n{content[:500]}...")
        else:
            print("ℹ️  No deadlock interventions were triggered")
        
        # Analyze the final state
        if pg_state_list:
            final_state = pg_state_list[-1]
            remaining_workpieces = 0
            for key, value in final_state.items():
                workpieces = [item for item in value if item.startswith('workpiece_')]
                remaining_workpieces += len(workpieces)
            
            print(f"\nFinal state analysis:")
            print(f"Remaining workpieces: {remaining_workpieces}")
            
            if remaining_workpieces == 0:
                print("🎉 TASK COMPLETED SUCCESSFULLY!")
            elif success_failure != 'failure over query time limit':
                print("✅ System avoided infinite loop and made progress!")
            else:
                print("⚠️  System hit query limit but may have made progress")
        
        # Save detailed results
        results_file = f"{Saving_path_result}/test_results.json"
        results = {
            "success_failure": success_failure,
            "iterations_completed": index_query_times + 1,
            "execution_time": execution_time,
            "total_tokens": sum(token_num_count_list),
            "deadlock_interventions": len(deadlock_files),
            "remaining_workpieces": remaining_workpieces if 'remaining_workpieces' in locals() else None
        }
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\nDetailed results saved to: {results_file}")
        
        return success_failure != 'failure over query time limit' or len(deadlock_files) > 0
        
    except Exception as e:
        print(f"❌ Execution failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🧪 Testing Deadlock Detection and Strategic Intervention Fix")
    print("=" * 60)
    
    # Test 1: Deadlock detection functionality
    detection_success = await test_deadlock_detection()
    
    # Test 2: Enhanced execution
    execution_success = await test_enhanced_execution()
    
    print("\n" + "=" * 60)
    print("📊 FINAL TEST RESULTS")
    print("=" * 60)
    
    if detection_success and execution_success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Deadlock detection is working correctly")
        print("✅ Strategic intervention is functioning")
        print("✅ System can break out of infinite loops")
    elif detection_success:
        print("⚠️  PARTIAL SUCCESS")
        print("✅ Deadlock detection is working correctly")
        print("❌ Execution test had issues")
    else:
        print("❌ TESTS FAILED")
        print("❌ Deadlock detection or strategic intervention not working")
    
    return detection_success and execution_success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
