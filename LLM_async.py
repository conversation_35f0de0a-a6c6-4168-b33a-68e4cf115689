import openai
import tiktoken
import time
import asyncio
import aiohttp
from typing import List, Dict, Tuple, Any, Optional

enc = tiktoken.get_encoding("cl100k_base")
assert enc.decode(enc.encode("hello world")) == "hello world"
enc = tiktoken.encoding_for_model("gpt-4")

# Enhanced LLM configuration with distributed endpoints
openai_api_key_name = 'sk-YyiBg6DSn1Fc2KBNU6ZYtw'

# Multiple endpoint configuration for load balancing and failover
ENDPOINTS = [
    {'url': 'http://*************:4033/v1', 'priority': 1, 'name': 'primary'},
    {'url': 'http://*************:1234/v1', 'priority': 2, 'name': 'secondary'},
    {'url': 'http://localhost:11434/v1', 'priority': 3, 'name': 'local_fallback'}
]

# Initialize OpenAI clients for different endpoints
endpoint_clients = {}
for endpoint in ENDPOINTS:
    try:
        client = openai.OpenAI(
            api_key=openai_api_key_name,
            base_url=endpoint['url']
        )
        endpoint_clients[endpoint['name']] = {
            'client': client,
            'url': endpoint['url'],
            'priority': endpoint['priority'],
            'available': True,
            'last_error': None
        }
        print(f"Initialized client for {endpoint['name']}: {endpoint['url']}")
    except Exception as e:
        print(f"Failed to initialize client for {endpoint['name']}: {str(e)}")

# For original OpenAI API (if needed)
openai_client = openai.OpenAI(
    api_key=openai_api_key_name
)

# Legacy client for backward compatibility
custom_client = endpoint_clients.get('primary', {}).get('client') if endpoint_clients else None

# Async client for OpenAI with proper timeout configuration
import httpx

# Create a custom HTTP client with longer timeouts
http_client = httpx.AsyncClient(
    timeout=httpx.Timeout(
        connect=30.0,  # Connection timeout
        read=120.0,    # Read timeout (for model inference)
        write=30.0,    # Write timeout
        pool=30.0      # Pool timeout
    ),
    limits=httpx.Limits(
        max_keepalive_connections=10,
        max_connections=20
    )
)

# Initialize async clients for different endpoints
async_endpoint_clients = {}
for endpoint in ENDPOINTS:
    try:
        async_client = openai.AsyncOpenAI(
            api_key=openai_api_key_name,
            base_url=endpoint['url'],
            http_client=http_client
        )
        async_endpoint_clients[endpoint['name']] = {
            'client': async_client,
            'url': endpoint['url'],
            'priority': endpoint['priority'],
            'available': True,
            'last_error': None
        }
        print(f"Initialized async client for {endpoint['name']}: {endpoint['url']}")
    except Exception as e:
        print(f"Failed to initialize async client for {endpoint['name']}: {str(e)}")

# Async client for original OpenAI API (if needed)
async_openai_client = openai.AsyncOpenAI(
    api_key=openai_api_key_name
)

# Legacy async client for backward compatibility
async_client = async_endpoint_clients.get('primary', {}).get('client') if async_endpoint_clients else None

def get_available_async_client():
    """Get the best available async client based on priority and availability"""
    # Sort clients by priority
    available_clients = [(name, info) for name, info in async_endpoint_clients.items() if info['available']]

    if not available_clients:
        print("No available async endpoints!")
        return None, None

    # Sort by priority (lower number = higher priority)
    available_clients.sort(key=lambda x: x[1]['priority'])

    best_client_name, best_client_info = available_clients[0]
    print(f"Using async endpoint: {best_client_name} ({best_client_info['url']})")

    return best_client_info['client'], best_client_name

def mark_async_endpoint_unavailable(endpoint_name, error_msg):
    """Mark an async endpoint as unavailable due to error"""
    if endpoint_name in async_endpoint_clients:
        async_endpoint_clients[endpoint_name]['available'] = False
        async_endpoint_clients[endpoint_name]['last_error'] = error_msg
        print(f"Marked async endpoint {endpoint_name} as unavailable: {error_msg}")

def mark_async_endpoint_available(endpoint_name):
    """Mark an async endpoint as available again"""
    if endpoint_name in async_endpoint_clients:
        async_endpoint_clients[endpoint_name]['available'] = True
        async_endpoint_clients[endpoint_name]['last_error'] = None
        print(f"Marked async endpoint {endpoint_name} as available again")

def get_model_for_framework(framework, agent_type='central', local_model_choice='gemma-3-27b'):
    """
    Get the appropriate model for a given framework and agent type.

    Args:
        framework: 'CMAS', 'DMAS', 'HMAS-1', 'HMAS-2'
        agent_type: 'central' or 'local' (for hierarchical frameworks)
        local_model_choice: 'gemma-3-27b' or 'qwen3-30b-a3b-mlx' (for DMAS and local agents)

    Returns:
        model_name: The appropriate model name
    """
    # Map user-friendly names to actual model names on the endpoint
    model_mapping = {
        'gemma-3-27b': 'gemma-3-27b-it-qat',
        'qwen3-235b-a22b': 'gemma-3-27b-it-qat',  # Fallback to working model
        'qwen3-30b-a3b-mlx': 'gemma-3-27b-it-qat'  # Fallback to working model
    }

    if framework == 'CMAS':
        # Use gemma-3-27b for central planner (since qwen3-235b-a22b seems unavailable)
        base_model = 'gemma-3-27b'
    elif framework == 'DMAS':
        base_model = local_model_choice
    elif framework in ['HMAS-1', 'HMAS-2']:
        if agent_type == 'central':
            # Use gemma-3-27b for central planner (since qwen3-235b-a22b seems unavailable)
            base_model = 'gemma-3-27b'
        else:  # local agent
            base_model = local_model_choice
    else:
        raise ValueError(f'Invalid framework: {framework}')

    # Return the actual model name used by the endpoint
    return model_mapping.get(base_model, base_model)

# Original synchronous GPT_response function (kept for backward compatibility)
def GPT_response(messages, model_name):
    token_num_count = 0
    for item in messages:
        token_num_count += len(enc.encode(item["content"]))

    # Support both old GPT models and new custom models
    supported_models = [
        'qwen3-235b-a22b', 'gemma-3-27b', 'qwen3-30b-a3b-mlx'
    ]

    if model_name in supported_models:
        # Choose the appropriate client based on model type
        if model_name.startswith('gpt-'):
            # Use original OpenAI API for GPT models
            client = openai_client
        else:
            # Use custom endpoint for new models
            client = custom_client

        try:
            result = client.chat.completions.create(
                model=model_name,
                messages=messages,
                temperature=0.0,
                top_p=1,
                frequency_penalty=0,
                presence_penalty=0,
                timeout=30  # Add explicit timeout
            )
        except Exception as e:
            print(f'First attempt failed: {str(e)}')
            try:
                result = client.chat.completions.create(
                    model=model_name,
                    messages=messages,
                    temperature=0.0,
                    top_p=1,
                    frequency_penalty=0,
                    presence_penalty=0,
                    timeout=30  # Add explicit timeout
                )
            except Exception as e:
                print(f'Second attempt failed: {str(e)}')
                try:
                    print(f'{model_name} Waiting 60 seconds for API query')
                    time.sleep(60)
                    result = client.chat.completions.create(
                        model=model_name,
                        messages=messages,
                        temperature=0.0,
                        top_p=1,
                        frequency_penalty=0,
                        presence_penalty=0,
                        timeout=30  # Add explicit timeout
                    )
                except Exception as e:
                    print(f'Final attempt failed: {str(e)}')
                    return 'Out of tokens', token_num_count

        # Extract response content using the new API structure
        response_content = result.choices[0].message.content
        token_num_count += len(enc.encode(response_content))
        print(f'Token_num_count: {token_num_count}')
        return response_content, token_num_count

    else:
        raise ValueError(f'Invalid model name: {model_name}')

# New asynchronous GPT_response function
async def async_GPT_response(messages, model_name, timeout=120):
    """
    Asynchronous version of GPT_response that doesn't block the main thread.

    Args:
        messages: List of message dictionaries
        model_name: Name of the model to use
        timeout: Timeout in seconds for the API call (increased default to 120s)

    Returns:
        Tuple of (response_content, token_num_count)
    """
    token_num_count = 0
    for item in messages:
        token_num_count += len(enc.encode(item["content"]))

    # Support both old GPT models and new custom models
    supported_models = [
        'qwen3-235b-a22b', 'gemma-3-27b', 'qwen3-30b-a3b-mlx', 'gemma-3-27b-it-qat'
    ]

    if model_name in supported_models:
        # Apply model mapping to get the actual model name used by the endpoint
        model_mapping = {
            'gemma-3-27b': 'gemma-3-27b-it-qat',
            'qwen3-235b-a22b': 'gemma-3-27b-it-qat',  # Fallback to working model
            'qwen3-30b-a3b-mlx': 'gemma-3-27b-it-qat'  # Fallback to working model
        }
        actual_model_name = model_mapping.get(model_name, model_name)
        print(f'Model mapping: {model_name} -> {actual_model_name}')
        # Choose the appropriate client based on model type with load balancing
        if model_name.startswith('gpt-'):
            # Use original OpenAI API for GPT models
            client = async_openai_client
            current_endpoint = 'openai'
        else:
            # Use load-balanced endpoint for custom models
            client, current_endpoint = get_available_async_client()
            if client is None:
                print("No available async endpoints for custom models")
                return 'No available endpoints', token_num_count

        # Enhanced retry logic with response monitoring and validation
        retry_delays = [5, 15, 30]  # Exponential backoff delays
        last_exception = None
        response_times = []  # Track response times for monitoring

        print(f'Making async API call to {actual_model_name} (mapped from {model_name}) with timeout {timeout}s, endpoint: {current_endpoint}')

        for retry_attempt, delay in enumerate(retry_delays):
            try:
                print(f'Attempt {retry_attempt+1}/{len(retry_delays)} - Starting API call to {current_endpoint}...')
                start_time = time.time()

                # Use asyncio.wait_for to add timeout to the API call
                result = await asyncio.wait_for(
                    client.chat.completions.create(
                        model=actual_model_name,  # Use the mapped model name
                        messages=messages,
                        temperature=0.0,
                        top_p=1,
                        frequency_penalty=0,
                        presence_penalty=0
                    ),
                    timeout=timeout
                )

                elapsed_time = time.time() - start_time
                response_times.append(elapsed_time)

                # Log response time statistics
                avg_response_time = sum(response_times) / len(response_times)
                print(f'API call successful in {elapsed_time:.2f}s (avg: {avg_response_time:.2f}s), endpoint: {current_endpoint}')

                # Mark endpoint as available if it was previously unavailable
                if current_endpoint != 'openai':
                    mark_async_endpoint_available(current_endpoint)

                # Extract and validate response content
                response_content = result.choices[0].message.content

                # Basic JSON validation for structured responses
                if response_content.strip().startswith('{') and response_content.strip().endswith('}'):
                    try:
                        import json
                        json.loads(response_content)
                        print("Response JSON validation: PASSED")
                    except json.JSONDecodeError as e:
                        print(f"Response JSON validation: FAILED - {str(e)}")
                        # Continue anyway, as some responses might be intentionally non-JSON

                token_num_count += len(enc.encode(response_content))
                print(f'Token_num_count: {token_num_count}')

                # Log performance warning for slow responses
                if elapsed_time > 20:
                    print(f"WARNING: Slow response detected ({elapsed_time:.2f}s) from {current_endpoint}")

                return response_content, token_num_count

            except asyncio.TimeoutError as e:
                elapsed_time = time.time() - start_time
                error_msg = f'Attempt {retry_attempt+1} timed out after {elapsed_time:.2f} seconds (limit: {timeout}s)'
                print(error_msg)

                # Mark current endpoint as unavailable and try next one
                if current_endpoint != 'openai':
                    mark_async_endpoint_unavailable(current_endpoint, error_msg)

                last_exception = asyncio.TimeoutError(f"API call timed out after {timeout} seconds")

            except asyncio.CancelledError as e:
                print(f'Attempt {retry_attempt+1} was cancelled: {str(e)}')
                last_exception = e

            except Exception as e:
                elapsed_time = time.time() - start_time
                error_msg = f'Attempt {retry_attempt+1} failed after {elapsed_time:.2f}s: {type(e).__name__}: {str(e)}'
                print(error_msg)

                # Mark current endpoint as unavailable for connection errors
                if current_endpoint != 'openai' and ('connection' in str(e).lower() or 'network' in str(e).lower()):
                    mark_async_endpoint_unavailable(current_endpoint, error_msg)

                last_exception = e

            # Try to switch to a different endpoint before waiting
            if retry_attempt < len(retry_delays) - 1 and not model_name.startswith('gpt-'):
                new_client, new_endpoint = get_available_async_client()
                if new_client and new_endpoint != current_endpoint:
                    client, current_endpoint = new_client, new_endpoint
                    print(f'Switching to new async endpoint: {current_endpoint}')
                    # Reduce delay when switching endpoints
                    delay = max(2, delay // 2)

            if retry_attempt < len(retry_delays) - 1:
                print(f'Waiting {delay} seconds before retry...')
                await asyncio.sleep(delay)

        # If we've exhausted all retries
        print(f'All {len(retry_delays)} attempts failed. Last error: {type(last_exception).__name__}: {str(last_exception)}')
        return 'Out of tokens', token_num_count
    else:
        raise ValueError(f'Invalid model name: {model_name}')

# Function to process multiple agent requests in parallel
async def process_agent_requests(agent_prompts, model_name, timeout=30):
    """
    Process multiple agent requests in parallel.
    
    Args:
        agent_prompts: List of (agent_id, messages) tuples
        model_name: Model name to use for all requests
        timeout: Timeout in seconds for each API call
        
    Returns:
        Dictionary mapping agent_id to (response, token_count)
    """
    tasks = []
    for agent_id, messages in agent_prompts:
        task = asyncio.create_task(async_GPT_response(messages, model_name, timeout))
        tasks.append((agent_id, task))
    
    results = {}
    for agent_id, task in tasks:
        try:
            response, token_count = await task
            results[agent_id] = (response, token_count)
        except Exception as e:
            print(f"Error processing agent {agent_id}: {str(e)}")
            results[agent_id] = ('Error: ' + str(e), 0)
    
    return results

# Async version of syntactic check function
async def async_with_action_syntactic_check_func(pg_dict_input, response, user_prompt_list_input,
                                               response_total_list_input, model_name,
                                               dialogue_history_method, max_iterations=6):
    """
    Asynchronous version of the syntactic check function.
    """
    import json
    import copy
    import re
    from prompt_env2 import message_construct_func

    user_prompt_list = copy.deepcopy(user_prompt_list_input)
    response_total_list = copy.deepcopy(response_total_list_input)
    iteration_num = 0
    token_num_count_list_add = []

    while iteration_num < max_iterations:
        response_total_list.append(response)
        try:
            # Try to parse the response as JSON
            original_response_dict = json.loads(response)

            # Import the action validation function
            from env2_create import action_from_response

            # Perform actual validation using the same logic as the sync version
            feedback = ''

            # Validate actions using the improved collision detection
            system_error_feedback, _, collision_check = action_from_response(pg_dict_input, original_response_dict)

            if system_error_feedback:
                feedback = system_error_feedback
            elif collision_check:
                feedback = 'Collision detected in your plan. Please revise to avoid conflicts. '

        except json.JSONDecodeError as e:
            feedback = 'Your assigned plan is not in the correct json format. Please reformat your response.'
        except Exception as e:
            feedback = f'Error validating your plan: {str(e)}. Please check your action format.'

        if feedback:
            feedback += ' Please replan for all the agents again with the same output format:'
            print('----------Syntactic Check----------')
            print(f'Response original: {response}')
            print(f'Feedback: {feedback}')
            user_prompt_list.append(feedback)

            # Construct messages for the API call
            messages = message_construct_func(user_prompt_list, response_total_list, dialogue_history_method)

            print(f'Length of messages {len(messages)}')
            response, token_num_count = await async_GPT_response(messages, model_name)
            token_num_count_list_add.append(token_num_count)

            print(f'Response new: {response}\n')
            if response == 'Out of tokens':
                return response, token_num_count_list_add

            iteration_num += 1
        else:
            return response, token_num_count_list_add

    return 'Syntactic Error', token_num_count_list_add
