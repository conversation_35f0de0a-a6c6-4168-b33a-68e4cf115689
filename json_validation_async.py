#!/usr/bin/env python3
"""
Async version of enhanced JSON validation for the multi-agent system

This module provides async versions of the JSON validation improvements
specifically for the async components of the system.
"""

import asyncio
import json
import logging
from typing import Dict, Tuple, Optional, List, Any
from json_validation_improvements import (
    enhanced_validator, 
    enhanced_json_extraction_and_validation,
    ValidationResult,
    JSONValidationError
)

logger = logging.getLogger(__name__)

async def async_enhanced_syntactic_check_with_deadlock_detection(
    pg_dict_input: Dict, 
    response: str,
    user_prompt_list: List[str],
    response_total_list: List[str],
    model_name: str,
    dialogue_history_method: str,
    max_iterations: int = 6
) -> Tuple[str, List[int]]:
    """
    Async enhanced version of syntactic check with deadlock detection
    
    This function can replace the existing async_with_action_syntactic_check_func
    with improved JSON validation and deadlock detection capabilities.
    
    Args:
        pg_dict_input: Current game state
        response: Initial response to validate
        user_prompt_list: List of user prompts
        response_total_list: List of previous responses
        model_name: Model name for API calls
        dialogue_history_method: Method for dialogue history construction
        max_iterations: Maximum validation iterations
        
    Returns:
        Tuple of (final_response, token_count_list)
    """
    import copy
    from prompt_env2 import message_construct_func
    from LLM_async import async_GPT_response
    from env2_create import action_from_response
    
    user_prompt_list = copy.deepcopy(user_prompt_list)
    response_total_list = copy.deepcopy(response_total_list)
    iteration_num = 0
    token_num_count_list_add = []
    
    # Reset validation history for new sequence
    enhanced_validator.clear_validation_history()
    
    while iteration_num < max_iterations:
        response_total_list.append(response)
        
        # Use enhanced JSON validation
        parsed_dict, validation_error = enhanced_json_extraction_and_validation(response)
        
        feedback = ''
        
        if validation_error.error_type == ValidationResult.DEADLOCK_DETECTED:
            logger.error("ASYNC DEADLOCK DETECTED in syntactic check - breaking validation loop")
            return 'Syntactic Error - Deadlock Detected', token_num_count_list_add
        
        if validation_error.error_type == ValidationResult.SUCCESS and parsed_dict:
            # Perform action validation using existing logic
            try:
                system_error_feedback, _, collision_check = action_from_response(pg_dict_input, parsed_dict)
                
                if system_error_feedback:
                    feedback = system_error_feedback
                elif collision_check:
                    feedback = 'Collision detected in your plan. Please revise to avoid conflicts. '
                    
            except Exception as e:
                feedback = f'Error validating your plan: {str(e)}. Please check your action format.'
        else:
            # Use enhanced error message with suggestions
            feedback = validation_error.error_message
            if validation_error.suggestions:
                feedback += ' Suggestions: ' + '; '.join(validation_error.suggestions[:3])
            feedback += ' Please reformat your response.'
        
        if feedback != '':
            feedback += ' Please replan for all the agents again with the same output format:'
            logger.info('----------Async Enhanced Syntactic Check----------')
            logger.info(f'Response original: {response}')
            logger.info(f'Validation error type: {validation_error.error_type.value}')
            logger.info(f'Feedback: {feedback}')
            
            user_prompt_list.append(feedback)
            messages = message_construct_func(user_prompt_list, response_total_list, dialogue_history_method)
            logger.info(f'Length of messages {len(messages)}')
            
            # Use async GPT response
            response, token_num_count = await async_GPT_response(messages, model_name)
            token_num_count_list_add.append(token_num_count)
            logger.info(f'Response new: {response}\n')
            
            if response == 'Out of tokens':
                return response, token_num_count_list_add
            iteration_num += 1
        else:
            logger.info("Async enhanced syntactic check passed successfully")
            return response, token_num_count_list_add
    
    logger.error(f"Async enhanced syntactic check failed after {max_iterations} iterations")
    return 'Syntactic Error', token_num_count_list_add


async def async_validate_coordination_responses(agent_responses: Dict[str, str]) -> Dict[str, Any]:
    """
    Async validation of multiple agent responses in coordination scenarios
    
    Args:
        agent_responses: Dictionary mapping agent_id to response string
        
    Returns:
        Dictionary with validation results for each agent
    """
    validation_results = {}
    
    for agent_id, response in agent_responses.items():
        parsed_dict, validation_error = enhanced_json_extraction_and_validation(response, reset_history=True)
        
        validation_results[agent_id] = {
            'parsed_dict': parsed_dict,
            'validation_error': validation_error,
            'is_valid': validation_error.error_type == ValidationResult.SUCCESS,
            'error_message': validation_error.error_message if validation_error.error_type != ValidationResult.SUCCESS else None
        }
    
    return validation_results


async def async_detect_coordination_deadlock(response_history: List[Dict[str, str]], 
                                           threshold: int = 3) -> Tuple[bool, Optional[str]]:
    """
    Detect deadlocks in coordination scenarios with multiple agents
    
    Args:
        response_history: List of response dictionaries from coordination rounds
        threshold: Number of identical rounds to consider a deadlock
        
    Returns:
        Tuple of (is_deadlock, deadlock_description)
    """
    if len(response_history) < threshold:
        return False, None
    
    # Check for identical coordination rounds
    recent_rounds = response_history[-threshold:]
    
    # Convert each round to a comparable format
    round_signatures = []
    for round_responses in recent_rounds:
        # Create signature based on agent responses
        signature = {}
        for agent_id, response in round_responses.items():
            parsed_dict, _ = enhanced_json_extraction_and_validation(response, reset_history=True)
            if parsed_dict:
                # Create a simplified signature of the actions
                actions = []
                for agent_key, action in parsed_dict.items():
                    actions.append(f"{agent_key}:{action}")
                signature[agent_id] = sorted(actions)
            else:
                signature[agent_id] = ["INVALID_RESPONSE"]
        round_signatures.append(signature)
    
    # Check if all recent rounds are identical
    if len(set(str(sig) for sig in round_signatures)) == 1:
        return True, f"Identical coordination responses repeated {threshold} times"
    
    # Check for alternating patterns
    if len(round_signatures) >= 4:
        if (round_signatures[-4] == round_signatures[-2] and 
            round_signatures[-3] == round_signatures[-1] and 
            round_signatures[-4] != round_signatures[-3]):
            return True, "Alternating coordination pattern detected"
    
    return False, None


class AsyncJSONValidationMonitor:
    """Monitor for tracking async JSON validation performance and issues"""
    
    def __init__(self):
        self.validation_stats = {
            'total_validations': 0,
            'successful_validations': 0,
            'failed_validations': 0,
            'deadlock_detections': 0,
            'error_types': {}
        }
        self.recent_errors = []
        self.max_recent_errors = 50
    
    def record_validation(self, validation_error: JSONValidationError):
        """Record a validation attempt for monitoring"""
        self.validation_stats['total_validations'] += 1
        
        if validation_error.error_type == ValidationResult.SUCCESS:
            self.validation_stats['successful_validations'] += 1
        else:
            self.validation_stats['failed_validations'] += 1
            
            # Track error types
            error_type = validation_error.error_type.value
            self.validation_stats['error_types'][error_type] = (
                self.validation_stats['error_types'].get(error_type, 0) + 1
            )
            
            if validation_error.error_type == ValidationResult.DEADLOCK_DETECTED:
                self.validation_stats['deadlock_detections'] += 1
            
            # Store recent error details
            self.recent_errors.append({
                'timestamp': asyncio.get_event_loop().time(),
                'error_type': error_type,
                'error_message': validation_error.error_message,
                'original_response': validation_error.original_response[:200] + '...' if len(validation_error.original_response) > 200 else validation_error.original_response
            })
            
            # Keep only recent errors
            if len(self.recent_errors) > self.max_recent_errors:
                self.recent_errors = self.recent_errors[-self.max_recent_errors:]
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """Get summary of validation statistics"""
        total = self.validation_stats['total_validations']
        if total == 0:
            return {'message': 'No validations recorded yet'}
        
        success_rate = (self.validation_stats['successful_validations'] / total) * 100
        
        return {
            'total_validations': total,
            'success_rate': f"{success_rate:.2f}%",
            'failed_validations': self.validation_stats['failed_validations'],
            'deadlock_detections': self.validation_stats['deadlock_detections'],
            'error_types': self.validation_stats['error_types'],
            'recent_errors_count': len(self.recent_errors)
        }
    
    def get_recent_errors(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent validation errors for debugging"""
        return self.recent_errors[-limit:] if self.recent_errors else []

# Global async validation monitor
async_validation_monitor = AsyncJSONValidationMonitor()


async def enhanced_async_json_validation_with_monitoring(response: str) -> Tuple[Optional[Dict], JSONValidationError]:
    """
    Enhanced async JSON validation with monitoring and statistics tracking
    
    Args:
        response: Raw response string to validate
        
    Returns:
        Tuple of (parsed_json_dict, validation_error)
    """
    parsed_dict, validation_error = enhanced_json_extraction_and_validation(response)
    
    # Record validation attempt for monitoring
    async_validation_monitor.record_validation(validation_error)
    
    return parsed_dict, validation_error
