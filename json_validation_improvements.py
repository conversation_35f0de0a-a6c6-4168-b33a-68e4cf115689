#!/usr/bin/env python3
"""
Enhanced JSON Validation and Response Processing Module

This module addresses the core JSON validation issues identified in the multi-agent system:
1. Improved JSON extraction from markdown code blocks and mixed text
2. Enhanced error handling and detailed logging
3. Deadlock detection for infinite validation loops
4. Robust response processing with fallback mechanisms
"""

import json
import re
import logging
from typing import Dict, Tuple, Optional, List, Any, Union
from dataclasses import dataclass
from enum import Enum
import time

# Configure logging for detailed error tracking
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ValidationResult(Enum):
    """Enumeration for validation results"""
    SUCCESS = "success"
    JSON_FORMAT_ERROR = "json_format_error"
    EXTRACTION_ERROR = "extraction_error"
    CONTENT_ERROR = "content_error"
    DEADLOCK_DETECTED = "deadlock_detected"

@dataclass
class JSONValidationError:
    """Detailed error information for JSON validation failures"""
    error_type: ValidationResult
    original_response: str
    extracted_json: Optional[str]
    error_message: str
    line_number: Optional[int] = None
    column_number: Optional[int] = None
    suggestions: List[str] = None

class EnhancedJSONValidator:
    """Enhanced JSON validator with improved extraction and error handling"""
    
    def __init__(self, max_validation_attempts: int = 6, deadlock_threshold: int = 3):
        self.max_validation_attempts = max_validation_attempts
        self.deadlock_threshold = deadlock_threshold
        self.validation_history: List[Dict[str, Any]] = []
        self.error_patterns: Dict[str, int] = {}
        
    def extract_json_from_response(self, response: str) -> Tuple[Optional[str], JSONValidationError]:
        """
        Enhanced JSON extraction from various response formats
        
        Args:
            response: Raw response string that may contain JSON
            
        Returns:
            Tuple of (extracted_json_string, error_info)
        """
        if not response or not response.strip():
            return None, JSONValidationError(
                error_type=ValidationResult.EXTRACTION_ERROR,
                original_response=response,
                extracted_json=None,
                error_message="Empty or whitespace-only response",
                suggestions=["Ensure the LLM provides a non-empty response"]
            )
        
        # Strategy 1: Direct JSON detection (current approach)
        json_match = re.search(r'{.*}', response, re.DOTALL)
        if json_match:
            extracted = json_match.group()
            validation_result = self._validate_json_string(extracted)
            if validation_result.error_type == ValidationResult.SUCCESS:
                return extracted, validation_result
        
        # Strategy 2: Markdown code block extraction
        code_block_patterns = [
            r'```json\s*(\{.*?\})\s*```',  # ```json { ... } ```
            r'```\s*(\{.*?\})\s*```',      # ``` { ... } ```
            r'`(\{.*?\})`',                # `{ ... }`
        ]
        
        for pattern in code_block_patterns:
            matches = re.findall(pattern, response, re.DOTALL)
            for match in matches:
                validation_result = self._validate_json_string(match)
                if validation_result.error_type == ValidationResult.SUCCESS:
                    return match, validation_result
        
        # Strategy 3: Line-by-line JSON detection
        lines = response.split('\n')
        for i, line in enumerate(lines):
            line = line.strip()
            if line.startswith('{') and line.endswith('}'):
                validation_result = self._validate_json_string(line)
                if validation_result.error_type == ValidationResult.SUCCESS:
                    return line, validation_result
        
        # Strategy 4: Multi-line JSON reconstruction
        json_lines = []
        in_json = False
        brace_count = 0
        
        for line in lines:
            line = line.strip()
            if '{' in line and not in_json:
                in_json = True
                brace_count = line.count('{') - line.count('}')
                json_lines = [line]
            elif in_json:
                json_lines.append(line)
                brace_count += line.count('{') - line.count('}')
                if brace_count <= 0:
                    reconstructed = '\n'.join(json_lines)
                    validation_result = self._validate_json_string(reconstructed)
                    if validation_result.error_type == ValidationResult.SUCCESS:
                        return reconstructed, validation_result
                    in_json = False
                    json_lines = []
        
        # If all strategies fail, return the best attempt with detailed error
        if json_match:
            extracted = json_match.group()
            return extracted, self._validate_json_string(extracted)
        
        return None, JSONValidationError(
            error_type=ValidationResult.EXTRACTION_ERROR,
            original_response=response,
            extracted_json=None,
            error_message="No valid JSON found in response using any extraction strategy",
            suggestions=[
                "Ensure response contains valid JSON in format: {\"Agent[x, y]\": \"action\"}",
                "Check for proper JSON formatting with quotes around keys and values",
                "Verify JSON is not truncated or malformed"
            ]
        )
    
    def _validate_json_string(self, json_str: str) -> JSONValidationError:
        """
        Validate a JSON string and provide detailed error information
        
        Args:
            json_str: String to validate as JSON
            
        Returns:
            JSONValidationError with validation results
        """
        try:
            parsed = json.loads(json_str)
            
            # Additional validation for agent response format
            if isinstance(parsed, dict):
                for key, value in parsed.items():
                    # Validate agent key format
                    if not re.match(r'Agent\[\d+\.?\d*, \d+\.?\d*\]', key):
                        return JSONValidationError(
                            error_type=ValidationResult.CONTENT_ERROR,
                            original_response=json_str,
                            extracted_json=json_str,
                            error_message=f"Invalid agent key format: {key}",
                            suggestions=[
                                "Use format: Agent[x.x, y.y] where x.x and y.y are coordinates",
                                "Ensure proper spacing: Agent[0.5, 0.5] not Agent[0.5,0.5]"
                            ]
                        )
                    
                    # Validate action format
                    if not isinstance(value, str) or not value.startswith('move('):
                        return JSONValidationError(
                            error_type=ValidationResult.CONTENT_ERROR,
                            original_response=json_str,
                            extracted_json=json_str,
                            error_message=f"Invalid action format: {value}",
                            suggestions=[
                                "Use format: move(workpiece_color, position[x.x, y.y])",
                                "Or: move(workpiece_color, target_color)"
                            ]
                        )
            
            return JSONValidationError(
                error_type=ValidationResult.SUCCESS,
                original_response=json_str,
                extracted_json=json_str,
                error_message="JSON validation successful"
            )
            
        except json.JSONDecodeError as e:
            return JSONValidationError(
                error_type=ValidationResult.JSON_FORMAT_ERROR,
                original_response=json_str,
                extracted_json=json_str,
                error_message=f"JSON decode error: {str(e)}",
                line_number=getattr(e, 'lineno', None),
                column_number=getattr(e, 'colno', None),
                suggestions=self._generate_json_fix_suggestions(json_str, e)
            )
    
    def _generate_json_fix_suggestions(self, json_str: str, error: json.JSONDecodeError) -> List[str]:
        """Generate specific suggestions for fixing JSON errors"""
        suggestions = []
        
        if "Expecting ',' delimiter" in str(error):
            suggestions.append("Add missing comma between JSON key-value pairs")
            suggestions.append("Check for trailing commas which are not allowed in JSON")
        
        if "Expecting ':' delimiter" in str(error):
            suggestions.append("Add missing colon ':' between key and value")
            suggestions.append("Ensure all keys are properly quoted")
        
        if "Expecting property name" in str(error):
            suggestions.append("Ensure all property names (keys) are enclosed in double quotes")
            suggestions.append("Check for missing opening or closing braces")
        
        if "Unterminated string" in str(error):
            suggestions.append("Check for missing closing quotes on string values")
            suggestions.append("Escape any quotes within string values")
        
        # Check for common formatting issues
        if "position(" in json_str and "position[" not in json_str:
            suggestions.append("Use square brackets for positions: position[x, y] not position(x, y)")
        
        if json_str.count('{') != json_str.count('}'):
            suggestions.append("Check for missing opening or closing braces { }")
        
        if json_str.count('"') % 2 != 0:
            suggestions.append("Check for missing or unescaped quotes")
        
        return suggestions
    
    def detect_validation_deadlock(self, current_response: str) -> bool:
        """
        Detect if we're in a validation deadlock (repeating same errors)
        
        Args:
            current_response: Current response being validated
            
        Returns:
            True if deadlock detected, False otherwise
        """
        # Add current attempt to history
        self.validation_history.append({
            'timestamp': time.time(),
            'response': current_response,
            'response_hash': hash(current_response)
        })
        
        # Keep only recent history
        if len(self.validation_history) > self.max_validation_attempts * 2:
            self.validation_history = self.validation_history[-self.max_validation_attempts:]
        
        # Check for repeated identical responses
        if len(self.validation_history) >= self.deadlock_threshold:
            recent_hashes = [entry['response_hash'] for entry in self.validation_history[-self.deadlock_threshold:]]
            if len(set(recent_hashes)) == 1:  # All identical
                logger.warning(f"DEADLOCK DETECTED: Same response repeated {self.deadlock_threshold} times")
                return True
        
        # Check for alternating pattern (A-B-A-B...)
        if len(self.validation_history) >= 4:
            hashes = [entry['response_hash'] for entry in self.validation_history[-4:]]
            if hashes[0] == hashes[2] and hashes[1] == hashes[3] and hashes[0] != hashes[1]:
                logger.warning("DEADLOCK DETECTED: Alternating response pattern")
                return True
        
        return False
    
    def clear_validation_history(self):
        """Clear validation history (call when starting new validation sequence)"""
        self.validation_history.clear()
        self.error_patterns.clear()

# Global validator instance
enhanced_validator = EnhancedJSONValidator()

def enhanced_json_extraction_and_validation(response: str, 
                                          reset_history: bool = False) -> Tuple[Optional[Dict], JSONValidationError]:
    """
    Main function for enhanced JSON extraction and validation
    
    Args:
        response: Raw response string
        reset_history: Whether to reset validation history
        
    Returns:
        Tuple of (parsed_json_dict, validation_error)
    """
    if reset_history:
        enhanced_validator.clear_validation_history()
    
    # Check for deadlock
    if enhanced_validator.detect_validation_deadlock(response):
        return None, JSONValidationError(
            error_type=ValidationResult.DEADLOCK_DETECTED,
            original_response=response,
            extracted_json=None,
            error_message="Validation deadlock detected - breaking infinite loop",
            suggestions=[
                "Try a completely different response format",
                "Reset the conversation context",
                "Use strategic intervention to break the deadlock"
            ]
        )
    
    # Extract and validate JSON
    extracted_json, error_info = enhanced_validator.extract_json_from_response(response)
    
    if error_info.error_type == ValidationResult.SUCCESS and extracted_json:
        try:
            parsed_dict = json.loads(extracted_json)
            logger.info(f"JSON validation successful: {len(parsed_dict)} agents")
            return parsed_dict, error_info
        except Exception as e:
            logger.error(f"Unexpected error parsing validated JSON: {e}")
            error_info.error_message = f"Unexpected parsing error: {str(e)}"
            error_info.error_type = ValidationResult.JSON_FORMAT_ERROR
    
    # Log detailed error information
    logger.error(f"JSON validation failed: {error_info.error_type.value}")
    logger.error(f"Error message: {error_info.error_message}")
    if error_info.suggestions:
        logger.info("Suggestions:")
        for suggestion in error_info.suggestions:
            logger.info(f"  - {suggestion}")
    
    return None, error_info


def improved_regex_json_extraction(response: str) -> Tuple[Optional[str], bool]:
    """
    Improved version of the existing regex JSON extraction

    This function replaces the current `re.search(r'{.*}', response, re.DOTALL)` pattern
    with more robust extraction logic while maintaining backward compatibility.

    Args:
        response: Raw response string

    Returns:
        Tuple of (extracted_json_string, success_flag)
    """
    extracted_json, error_info = enhanced_validator.extract_json_from_response(response)

    if error_info.error_type == ValidationResult.SUCCESS:
        return extracted_json, True
    elif extracted_json:  # Partial extraction successful
        return extracted_json, False
    else:
        # Fallback to original regex for backward compatibility
        match = re.search(r'{.*}', response, re.DOTALL)
        if match:
            return match.group(), False
        return None, False


def enhanced_syntactic_check_with_deadlock_detection(pg_dict_input: Dict,
                                                   response: str,
                                                   user_prompt_list: List[str],
                                                   response_total_list: List[str],
                                                   model_name: str,
                                                   dialogue_history_method: str,
                                                   max_iterations: int = 6) -> Tuple[str, List[int]]:
    """
    Enhanced version of syntactic check with deadlock detection and improved error handling

    This function can replace the existing with_action_syntactic_check_func with improved
    JSON validation and deadlock detection capabilities.

    Args:
        pg_dict_input: Current game state
        response: Initial response to validate
        user_prompt_list: List of user prompts
        response_total_list: List of previous responses
        model_name: Model name for API calls
        dialogue_history_method: Method for dialogue history construction
        max_iterations: Maximum validation iterations

    Returns:
        Tuple of (final_response, token_count_list)
    """
    import copy
    from prompt_env2 import message_construct_func
    from LLM import GPT_response
    from env2_create import action_from_response

    user_prompt_list = copy.deepcopy(user_prompt_list)
    response_total_list = copy.deepcopy(response_total_list)
    iteration_num = 0
    token_num_count_list_add = []

    # Reset validation history for new sequence
    enhanced_validator.clear_validation_history()

    while iteration_num < max_iterations:
        response_total_list.append(response)

        # Use enhanced JSON validation
        parsed_dict, validation_error = enhanced_json_extraction_and_validation(response)

        feedback = ''

        if validation_error.error_type == ValidationResult.DEADLOCK_DETECTED:
            logger.error("DEADLOCK DETECTED in syntactic check - breaking validation loop")
            return 'Syntactic Error - Deadlock Detected', token_num_count_list_add

        if validation_error.error_type == ValidationResult.SUCCESS and parsed_dict:
            # Perform action validation using existing logic
            try:
                system_error_feedback, _, collision_check = action_from_response(pg_dict_input, parsed_dict)

                if system_error_feedback:
                    feedback = system_error_feedback
                elif collision_check:
                    feedback = 'Collision detected in your plan. Please revise to avoid conflicts. '

            except Exception as e:
                feedback = f'Error validating your plan: {str(e)}. Please check your action format.'
        else:
            # Use enhanced error message with suggestions
            feedback = validation_error.error_message
            if validation_error.suggestions:
                feedback += ' Suggestions: ' + '; '.join(validation_error.suggestions[:3])
            feedback += ' Please reformat your response.'

        if feedback != '':
            feedback += ' Please replan for all the agents again with the same output format:'
            logger.info('----------Enhanced Syntactic Check----------')
            logger.info(f'Response original: {response}')
            logger.info(f'Validation error type: {validation_error.error_type.value}')
            logger.info(f'Feedback: {feedback}')

            user_prompt_list.append(feedback)
            messages = message_construct_func(user_prompt_list, response_total_list, dialogue_history_method)
            logger.info(f'Length of messages {len(messages)}')

            response, token_num_count = GPT_response(messages, model_name)
            token_num_count_list_add.append(token_num_count)
            logger.info(f'Response new: {response}\n')

            if response == 'Out of tokens':
                return response, token_num_count_list_add
            iteration_num += 1
        else:
            logger.info("Enhanced syntactic check passed successfully")
            return response, token_num_count_list_add

    logger.error(f"Enhanced syntactic check failed after {max_iterations} iterations")
    return 'Syntactic Error', token_num_count_list_add


def log_json_validation_statistics():
    """Log statistics about JSON validation patterns and errors"""
    if enhanced_validator.validation_history:
        logger.info("=== JSON Validation Statistics ===")
        logger.info(f"Total validation attempts: {len(enhanced_validator.validation_history)}")

        # Analyze error patterns
        if enhanced_validator.error_patterns:
            logger.info("Common error patterns:")
            for pattern, count in enhanced_validator.error_patterns.items():
                logger.info(f"  {pattern}: {count} occurrences")

        # Recent validation timeline
        recent_attempts = enhanced_validator.validation_history[-5:]
        logger.info("Recent validation attempts:")
        for i, attempt in enumerate(recent_attempts):
            logger.info(f"  {i+1}. Hash: {attempt['response_hash']} at {attempt['timestamp']}")


# Backward compatibility functions for easy integration
def extract_json_from_markdown_response(response: str) -> Optional[str]:
    """
    Backward compatible function that can replace existing regex extraction

    Args:
        response: Raw response string

    Returns:
        Extracted JSON string or None
    """
    extracted_json, success = improved_regex_json_extraction(response)
    return extracted_json


def validate_agent_response_format(response_dict: Dict) -> Tuple[bool, str]:
    """
    Validate that a parsed response dictionary has the correct agent format

    Args:
        response_dict: Parsed JSON dictionary

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not isinstance(response_dict, dict):
        return False, "Response must be a dictionary"

    for key, value in response_dict.items():
        # Validate agent key format
        if not re.match(r'Agent\[\d+\.?\d*, \d+\.?\d*\]', key):
            return False, f"Invalid agent key format: {key}. Use Agent[x.x, y.y]"

        # Validate action format
        if not isinstance(value, str) or not value.startswith('move('):
            return False, f"Invalid action format: {value}. Use move(workpiece, target)"

    return True, "Valid format"
