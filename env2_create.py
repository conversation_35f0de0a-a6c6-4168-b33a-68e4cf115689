# Workpiece moving to target with collisions

from prompt_env2 import *
from LLM import *
from sre_constants import error
import random
import os
import json
import re
import copy
import numpy as np
import shutil
import time

def corner_position(pg_row_i, pg_column_j):
  corner_position_list = [(float(pg_row_i), float(pg_column_j)), (float(pg_row_i), float(pg_column_j + 1)), (float(pg_row_i + 1), float(pg_column_j)),
   (float(pg_row_i + 1), float(pg_column_j + 1))]
  return corner_position_list

def judge_move_box2pos_box2target_func(key, value, pg_dict_original):
  # Validate agent position
  if not (str(key[0] - 0.5) + '_' + str(key[1] - 0.5) in pg_dict_original.keys() \
          and str(key[0] - 0.5) + '_' + str(key[1] + 0.5) in pg_dict_original.keys() \
          and str(key[0] + 0.5) + '_' + str(key[1] - 0.5) in pg_dict_original.keys() \
          and str(key[0] + 0.5) + '_' + str(key[1] + 0.5) in pg_dict_original.keys() \
          and np.mod(key[0], 1) == 0.5 and np.mod(key[1], 1) == 0.5):
    return None, False, False, f'Agent[{float(key[0])}, {float(key[1])}] is not in the agent list. '

  # Find workpiece location within agent's reach
  workpiece_location = None
  adjacent_positions = [
    (key[0] - 0.5, key[1] - 0.5),
    (key[0] - 0.5, key[1] + 0.5),
    (key[0] + 0.5, key[1] - 0.5),
    (key[0] + 0.5, key[1] + 0.5)
  ]

  for pos in adjacent_positions:
    pos_key = str(pos[0]) + '_' + str(pos[1])
    if pos_key in pg_dict_original and value[0] in pg_dict_original[pos_key]:
      workpiece_location = pos
      break

  if workpiece_location is None:
    # Provide detailed feedback about why the workpiece is not reachable
    available_workpieces = []
    for pos in adjacent_positions:
      pos_key = str(pos[0]) + '_' + str(pos[1])
      if pos_key in pg_dict_original:
        available_workpieces.extend(pg_dict_original[pos_key])

    feedback = f'Workpiece {value[0]} not found in agent {key} reach. Available workpieces: {available_workpieces}; '
    return None, False, False, feedback

  # Validate move action
  if type(value[1]) == tuple:
    # Moving to a position - check if it's adjacent and valid
    if np.abs(key[0]-value[1][0])==0.5 and np.abs(key[1]-value[1][1])==0.5:
      return workpiece_location, True, False, ''
    else:
      return None, False, False, f'Target position {value[1]} is not adjacent to agent {key}; '

  elif type(value[1]) == str:
    # Moving to a target - validate target exists and color matches
    agent_pos_key = str(key[0])+'_'+str(key[1])
    if value[1] in pg_dict_original[agent_pos_key] and value[0][:10] == 'workpiece_' and value[1][:7] == 'target_' and value[0][10:] == value[1][7:]:
      return workpiece_location, False, True, ''
    else:
      available_targets = [item for item in pg_dict_original[agent_pos_key] if item.startswith('target_')]
      feedback = f'Target {value[1]} not valid for workpiece {value[0]} at agent {key}. Available targets: {available_targets}; '
      return None, False, False, feedback
  else:
    return None, False, False, f'Invalid action format for agent {key}: {value[1]}; '


def state_update_func(pg_row_num, pg_column_num, pg_dict):
  pg_dict_copy = copy.deepcopy(pg_dict)
  state_update_prompt = ''
  for i in range(pg_row_num):
    for j in range(pg_column_num):
      square_item_list = pg_dict_copy[str(i + 0.5) + '_' + str(j + 0.5)]
      state_update_prompt += f'Agent[{i+0.5}, {j+0.5}]: I am in square[{i+0.5}, {j+0.5}], I can observe {square_item_list}, I can do '
      action_list = []
      for corner_x, corner_y in corner_position(i, j):
        if len(pg_dict_copy[str(corner_x)+'_'+str(corner_y)]) == 1:
          workpiece = pg_dict_copy[str(corner_x)+'_'+str(corner_y)][0]
          for surround_index in corner_position(i, j):
            if surround_index != (corner_x, corner_y):
              action_list.append(f'move({workpiece}, position{surround_index})')
          if 'target'+workpiece[10:] in pg_dict_copy[str(i+0.5)+'_'+str(j+0.5)]:
            action_list.append(f'move({workpiece}, target{workpiece[10:]})')
      state_update_prompt += f'{action_list}\n'
  return state_update_prompt

def state_update_func_local_agent(pg_row_num, pg_column_num, pg_row_i, pg_column_j, pg_dict):
  pg_dict_copy = copy.deepcopy(pg_dict)
  state_update_prompt_local_agent = ''
  state_update_prompt_other_agent = ''

  for i in range(pg_row_num):
    for j in range(pg_column_num):
      if not (i == pg_row_i and pg_column_j == j):
        square_item_list = pg_dict_copy[str(i + 0.5) + '_' + str(j + 0.5)]
        state_update_prompt_other_agent += f'Agent[{i+0.5}, {j+0.5}]: I am in square[{i+0.5}, {j+0.5}], I can observe {square_item_list}, I can do '
        action_list = []
        for corner_x, corner_y in corner_position(i, j):
          if len(pg_dict_copy[str(corner_x) + '_' + str(corner_y)]) == 1:
            workpiece = pg_dict_copy[str(corner_x) + '_' + str(corner_y)][0]
            for surround_index in corner_position(i, j):
              if surround_index != (corner_x, corner_y):
                action_list.append(f'move({workpiece}, position{surround_index})')
            if 'target' + workpiece[10:] in pg_dict_copy[str(i + 0.5) + '_' + str(j + 0.5)]:
              action_list.append(f'move({workpiece}, target{workpiece[10:]})')
        state_update_prompt_other_agent += f'{action_list}\n'

  state_update_prompt_local_agent += f'Agent[{pg_row_i+0.5}, {pg_column_j+0.5}]: in square[{pg_row_i+0.5}, {pg_column_j+0.5}], can observe {square_item_list}, can do '
  action_list = []
  for corner_x, corner_y in corner_position(pg_row_i, pg_column_j):
    if len(pg_dict_copy[str(corner_x) + '_' + str(corner_y)]) == 1:
      workpiece = pg_dict_copy[str(corner_x) + '_' + str(corner_y)][0]
      for surround_index in corner_position(pg_row_i, pg_column_j):
        if surround_index != (corner_x, corner_y):
          action_list.append(f'move({workpiece}, position{surround_index})')
      if 'target' + workpiece[10:] in pg_dict_copy[str(i + 0.5) + '_' + str(j + 0.5)]:
        action_list.append(f'move({workpiece}, target{workpiece[10:]})')
  state_update_prompt_local_agent += f'{action_list}\n'
  return state_update_prompt_local_agent, state_update_prompt_other_agent

def with_action_syntactic_check_func(pg_dict_input, response, user_prompt_list_input, response_total_list_input, model_name, dialogue_history_method):
  user_prompt_list = copy.deepcopy(user_prompt_list_input)
  response_total_list = copy.deepcopy(response_total_list_input)
  iteration_num = 0
  token_num_count_list_add = []
  while iteration_num < 6:
    response_total_list.append(response)
    try:
      original_response_dict = json.loads(response)

      # Use the improved action validation logic
      system_error_feedback, _, collision_check = action_from_response(pg_dict_input, original_response_dict)

      feedback = ''
      if system_error_feedback:
        feedback = system_error_feedback
      elif collision_check:
        feedback = 'Collision detected in your plan. Please revise to avoid conflicts. '

    except json.JSONDecodeError:
      feedback = 'Your assigned plan is not in the correct json format as before. If your answer is empty dict, please check whether you miss to move workpiece into the same colored target like move(workpiece_blue, target_blue)'
    except Exception as e:
      feedback = f'Error validating your plan: {str(e)}. Please check your action format.'

    if feedback != '':
      feedback += 'Please replan for all the agents again with the same ouput format:'
      print('----------Syntactic Check----------')
      print(f'Response original: {response}')
      print(f'Feedback: {feedback}')
      user_prompt_list.append(feedback)
      messages = message_construct_func(user_prompt_list, response_total_list, dialogue_history_method) # message construction
      print(f'Length of messages {len(messages)}')
      response, token_num_count = GPT_response(messages, model_name)
      token_num_count_list_add.append(token_num_count)
      print(f'Response new: {response}\n')
      if response == 'Out of tokens':
        return response, token_num_count_list_add
      iteration_num += 1
    else:
      return response, token_num_count_list_add
  return 'Syntactic Error', token_num_count_list_add

def action_from_response(pg_dict_input, original_response_dict):
  collision_check = False
  system_error_feedback = ''
  pg_dict_original = copy.deepcopy(pg_dict_input)
  transformed_dict = {}

  # Parse all actions first
  for key, value in original_response_dict.items():
    coordinates = tuple(map(float, re.findall(r"\d+\.?\d*", key)))

    # match the item and location in the value
    match = re.match(r"move\((.*?),\s(.*?)\)", value)
    if match:
      item, location = match.groups()
      if "position" in location:
          location = tuple(map(float, re.findall(r"\d+\.?\d*", location)))
      transformed_dict[coordinates] = [item, location]

  # CRITICAL FIX: Check for collisions BEFORE executing any actions
  destination_positions = {}  # Track what's being moved to each position
  valid_actions = {}  # Only store actions that pass validation

  # First pass: Validate all actions and detect conflicts
  for key, value in transformed_dict.items():
    print(f"Validating - Key: {key}, Value1: {value[0]}, Value2: {value[1]}")
    workpiece_location, judge_move_box2pos, judge_move_box2target, feedback = judge_move_box2pos_box2target_func(key, value, pg_dict_original)

    if judge_move_box2pos == True:
      # Check if destination position already has a workpiece or if another action targets it
      dest_key = str(value[1][0]) + '_' + str(value[1][1])

      # Check if destination already has workpieces
      if len(pg_dict_original[dest_key]) > 0:
        system_error_feedback += f'Position {dest_key} already occupied, cannot move {value[0]} there; '
        collision_check = True
        continue

      # Check if another action is also targeting this position
      if dest_key in destination_positions:
        system_error_feedback += f'Collision detected: Multiple workpieces trying to move to position {dest_key}; '
        collision_check = True
        continue

      # Mark this destination as targeted
      destination_positions[dest_key] = value[0]
      valid_actions[key] = (value, workpiece_location, 'move_to_pos')

    elif judge_move_box2target == True:
      valid_actions[key] = (value, workpiece_location, 'move_to_target')

    else:
      system_error_feedback += f'Your assigned task for {key[0]}_{key[1]} is not in the doable action list; '

  # If there are collisions, don't execute any actions
  if collision_check:
    print(f"COLLISION PREVENTION: Actions blocked due to conflicts")
    return system_error_feedback, pg_dict_original, collision_check

  # Second pass: Execute only valid actions
  for key, (value, workpiece_location, action_type) in valid_actions.items():
    if action_type == 'move_to_pos':
      pg_dict_original[str(workpiece_location[0])+'_'+str(workpiece_location[1])].remove(value[0])
      pg_dict_original[str(value[1][0])+'_'+str(value[1][1])].append(value[0])
      print(f"Executed: Moved {value[0]} from {workpiece_location} to {value[1]}")
    elif action_type == 'move_to_target':
      pg_dict_original[str(workpiece_location[0])+'_'+str(workpiece_location[1])].remove(value[0])
      pg_dict_original[str(key[0])+'_'+str(key[1])].remove(value[1])
      print(f"Executed: Moved {value[0]} to target {value[1]}")

  return system_error_feedback, pg_dict_original, collision_check

def env_create(pg_row_num = 5, pg_column_num = 5, workpiece_num_low_bound = 2, workpiece_num_upper_bound = 2, color_list = ['blue', 'red', 'green', 'purple', 'orange']):
  # pg_dict records the items in each square over steps, here in the initial setting, we randomly assign items into each square
  pg_dict = {}
  for i in range(pg_row_num):
    for j in range(pg_column_num):
      pg_dict[str(i+0.5)+'_'+str(j+0.5)] = []
  for i in range(pg_row_num+1):
    for j in range(pg_column_num+1):
      pg_dict[str(float(i))+'_'+str(float(j))] = []

  for color in color_list:
    workpiece_num = random.randint(workpiece_num_low_bound, workpiece_num_upper_bound)
    for _ in range(workpiece_num):
      N_box = random.randint(0, pg_row_num*pg_column_num - 1)
      a_box = N_box // pg_column_num
      b_box = N_box % pg_column_num
      N_target = random.randint(0, pg_row_num*pg_column_num - 1)
      a_target = N_target // pg_column_num
      b_target = N_target % pg_column_num
      corner_list = [(1.0, 0.0), (0.0, 0.0), (0.0, 1.0), (1.0, 1.0)]
      random.shuffle(corner_list)
      for random_x, random_y in corner_list:
        if len(pg_dict[str(float(a_box) + random_x)+'_'+str(float(b_box) + random_y)]) == 0:
          pg_dict[str(float(a_box) + random_x) + '_' + str(float(b_box) + random_y)].append('workpiece_' + color)
          pg_dict[str(a_target+0.5)+'_'+str(b_target+0.5)].append('target_' + color)
          break
  print(pg_dict)
  print('\n')
  return pg_dict

def create_env2(Saving_path, repeat_num = 10):
  if not os.path.exists(Saving_path):
    os.makedirs(Saving_path, exist_ok=True)
  else:
    shutil.rmtree(Saving_path)
    os.makedirs(Saving_path, exist_ok=True)

  for i ,j in [(2,2), (2,4), (4,4), (4,8)]:
    if not os.path.exists(Saving_path+f'/env_pg_state_{i}_{j}'):
      os.makedirs(Saving_path+f'/env_pg_state_{i}_{j}', exist_ok=True)
    else:
      shutil.rmtree(Saving_path+f'/env_pg_state_{i}_{j}')
      os.makedirs(Saving_path+f'/env_pg_state_{i}_{j}', exist_ok=True)

    for iteration_num in range(repeat_num):
      # Define the total row and column numbers of the whole playground, and the item number of each colored target and workpiece
      pg_row_num = i; pg_column_num = j; workpiece_num_low_bound = 1; workpiece_num_upper_bound = 1
      # Define the used colors
      color_list = ['blue', 'red', 'green', 'purple', 'orange']
      pg_dict = env_create(pg_row_num, pg_column_num, workpiece_num_low_bound, workpiece_num_upper_bound, color_list)
      os.makedirs(Saving_path+f'/env_pg_state_{i}_{j}/pg_state{iteration_num}', exist_ok=True)
      with open(Saving_path+f'/env_pg_state_{i}_{j}/pg_state{iteration_num}/pg_state{iteration_num}.json', 'w') as f:
        json.dump(pg_dict, f)

if __name__ == "__main__":
    import os
    Code_dir_path = os.path.dirname(os.path.abspath(__file__)) + '/' # Current directory path
    Saving_path = Code_dir_path + 'Env2_WorkpieceNet2'
    # The first time to create the environment, after that you can comment it
    create_env2(Saving_path, repeat_num = 10)