import openai
import tiktoken
import time
import logging
import requests.exceptions

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(levelname)s - %(message)s',
                   handlers=[logging.StreamHandler()])
logger = logging.getLogger(__name__)

enc = tiktoken.get_encoding("cl100k_base")
assert enc.decode(enc.encode("hello world")) == "hello world"
enc = tiktoken.encoding_for_model("gpt-4")

# Enhanced LLM configuration with distributed endpoints
openai_api_key_name = 'sk-YyiBg6DSn1Fc2KBNU6ZYtw'

# Multiple endpoint configuration for load balancing and failover
ENDPOINTS = [
    {'url': 'http://*************:4033/v1', 'priority': 1, 'name': 'primary'},
    {'url': 'http://*************:1234/v1', 'priority': 2, 'name': 'secondary'},
    {'url': 'http://localhost:11434/v1', 'priority': 3, 'name': 'local_fallback'}
]

# 设置请求超时（秒）
REQUEST_TIMEOUT = 30

# Initialize OpenAI clients for different endpoints
endpoint_clients = {}
for endpoint in ENDPOINTS:
    try:
        client = openai.OpenAI(
            api_key=openai_api_key_name,
            base_url=endpoint['url']
        )
        endpoint_clients[endpoint['name']] = {
            'client': client,
            'url': endpoint['url'],
            'priority': endpoint['priority'],
            'available': True,
            'last_error': None
        }
        logger.info(f"Initialized client for {endpoint['name']}: {endpoint['url']}")
    except Exception as e:
        logger.error(f"Failed to initialize client for {endpoint['name']}: {str(e)}")

# For original OpenAI API (if needed)
openai_client = openai.OpenAI(
    api_key=openai_api_key_name
)

# Legacy client for backward compatibility
custom_client = endpoint_clients.get('primary', {}).get('client') if endpoint_clients else None

def get_available_client():
    """Get the best available client based on priority and availability"""
    # Sort clients by priority
    available_clients = [(name, info) for name, info in endpoint_clients.items() if info['available']]

    if not available_clients:
        logger.error("No available endpoints!")
        return None, None

    # Sort by priority (lower number = higher priority)
    available_clients.sort(key=lambda x: x[1]['priority'])

    best_client_name, best_client_info = available_clients[0]
    logger.info(f"Using endpoint: {best_client_name} ({best_client_info['url']})")

    return best_client_info['client'], best_client_name

def mark_endpoint_unavailable(endpoint_name, error_msg):
    """Mark an endpoint as unavailable due to error"""
    if endpoint_name in endpoint_clients:
        endpoint_clients[endpoint_name]['available'] = False
        endpoint_clients[endpoint_name]['last_error'] = error_msg
        logger.warning(f"Marked endpoint {endpoint_name} as unavailable: {error_msg}")

def mark_endpoint_available(endpoint_name):
    """Mark an endpoint as available again"""
    if endpoint_name in endpoint_clients:
        endpoint_clients[endpoint_name]['available'] = True
        endpoint_clients[endpoint_name]['last_error'] = None
        logger.info(f"Marked endpoint {endpoint_name} as available again")

def get_model_for_framework(framework, agent_type='central', local_model_choice='gemma-3-27b'):
    """
    Get the appropriate model for a given framework and agent type.

    Args:
        framework: 'CMAS', 'DMAS', 'HMAS-1', 'HMAS-2'
        agent_type: 'central' or 'local' (for hierarchical frameworks)
        local_model_choice: 'gemma-3-27b' or 'qwen3-30b-a3b-mlx' (for DMAS and local agents)

    Returns:
        model_name: The appropriate model name
    """
    if framework == 'CMAS':
        # Use qwen3-235b-a22b for central planners
        return 'qwen3-235b-a22b'
    elif framework == 'DMAS':
        return local_model_choice
    elif framework in ['HMAS-1', 'HMAS-2']:
        if agent_type == 'central':
            # Use qwen3-235b-a22b for central planners
            return 'qwen3-235b-a22b'
        else:  # local agent
            return local_model_choice
    else:
        raise ValueError(f'Invalid framework: {framework}')



def GPT_response(messages, model_name):
  token_num_count = 0
  for item in messages:
    token_num_count += len(enc.encode(item["content"]))

  # Support both old GPT models and new custom models
  supported_models = [
      'qwen3-235b-a22b', 'gemma-3-27b', 'qwen3-30b-a3b-mlx'
  ]

  if model_name in supported_models:
    logger.info(f'正在使用模型: {model_name}，消息长度: {token_num_count} tokens')

    # Choose the appropriate client based on model type
    if model_name.startswith('gpt-'):
        # Use original OpenAI API for GPT models
        client = openai_client
        current_endpoint = 'openai'
    else:
        # Use load-balanced endpoint for custom models
        client, current_endpoint = get_available_client()
        if client is None:
            logger.error("No available endpoints for custom models")
            return 'No available endpoints', token_num_count

    # Enhanced retry logic with endpoint failover
    max_attempts = 3
    for attempt in range(1, max_attempts + 1):
      try:
        logger.info(f'API调用尝试 #{attempt} - 模型: {model_name}，端点: {current_endpoint}，设置超时: {REQUEST_TIMEOUT}秒')

        start_time = time.time()
        result = client.chat.completions.create(
          model=model_name,
          messages=messages,
          temperature=0.0,
          top_p=1,
          frequency_penalty=0,
          presence_penalty=0,
          timeout=REQUEST_TIMEOUT
        )

        elapsed_time = time.time() - start_time
        logger.info(f'API调用成功 - 耗时: {elapsed_time:.2f}秒，端点: {current_endpoint}')

        # Mark endpoint as available if it was previously unavailable
        if current_endpoint != 'openai':
            mark_endpoint_available(current_endpoint)

        # Extract response content using the new API structure
        response_content = result.choices[0].message.content
        token_num_count += len(enc.encode(response_content))
        logger.info(f'总Token数量: {token_num_count}')
        return response_content, token_num_count

      except requests.exceptions.Timeout:
        error_msg = f'API请求超时（尝试 #{attempt}） - 超过 {REQUEST_TIMEOUT}秒'
        logger.error(error_msg)

        # Mark current endpoint as unavailable and try next one
        if current_endpoint != 'openai':
            mark_endpoint_unavailable(current_endpoint, error_msg)

        if attempt < max_attempts:
            # Try to get a different endpoint
            if not model_name.startswith('gpt-'):
                new_client, new_endpoint = get_available_client()
                if new_client and new_endpoint != current_endpoint:
                    client, current_endpoint = new_client, new_endpoint
                    logger.info(f'切换到新端点: {current_endpoint}')
                    continue
        else:
          return f'API Timeout - 所有端点请求超时，超过{REQUEST_TIMEOUT}秒', token_num_count

      except requests.exceptions.ConnectionError as e:
        error_msg = f'API连接错误（尝试 #{attempt}）: {str(e)}'
        logger.error(error_msg)

        # Mark current endpoint as unavailable and try next one
        if current_endpoint != 'openai':
            mark_endpoint_unavailable(current_endpoint, error_msg)

        if attempt < max_attempts:
            # Try to get a different endpoint
            if not model_name.startswith('gpt-'):
                new_client, new_endpoint = get_available_client()
                if new_client and new_endpoint != current_endpoint:
                    client, current_endpoint = new_client, new_endpoint
                    logger.info(f'切换到新端点: {current_endpoint}')
                    continue
        else:
          return f'Connection Error - 所有端点连接失败: {str(e)}', token_num_count

      except Exception as e:
        error_msg = str(e)
        logger.error(f'API调用失败（尝试 #{attempt}）: {error_msg}')

        # Mark current endpoint as unavailable for non-transient errors
        if current_endpoint != 'openai' and 'rate limit' not in error_msg.lower():
            mark_endpoint_unavailable(current_endpoint, error_msg)

        # 最后一次尝试前等待更长时间
        if attempt < max_attempts:
          wait_time = 20 * attempt  # 第一次20秒，第二次40秒
          logger.info(f'等待 {wait_time} 秒后重试...')
          time.sleep(wait_time)

          # Try to get a different endpoint for non-gpt models
          if not model_name.startswith('gpt-'):
              new_client, new_endpoint = get_available_client()
              if new_client and new_endpoint != current_endpoint:
                  client, current_endpoint = new_client, new_endpoint
                  logger.info(f'切换到新端点: {current_endpoint}')
        else:
          # 最后一次尝试失败
          return f'API Error - 所有端点失败: {error_msg}', token_num_count

  else:
    raise ValueError(f'Invalid model name: {model_name}')
